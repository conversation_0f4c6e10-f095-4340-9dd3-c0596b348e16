import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_add_page.dart';
import 'package:qiazhun/modules/account/account_common_widgets.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/modules/account/section_header.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class AccountAddCreditSection extends StatefulWidget {
  final AccountAddController? addController;
  final AccountModel? accountModel;
  const AccountAddCreditSection({super.key, this.addController, this.accountModel});

  @override
  State<StatefulWidget> createState() => _AccountAddCreditState();
}

class _AccountAddCreditState extends State<AccountAddCreditSection> with WidgetsBindingObserver, AutomaticKeepAliveClientMixin {
  final DateFormat _dateFormat = DateFormat('MM/dd');
  int? _billingDate;
  int? _paymentDate;
  DateTime? _annualFeeDate;
  BankItem? _selectedBankItem;

  bool _inAssets = true;

  int _annualFeeType = 1;
  List<String> _annualFeeStr = ['刷卡次数', '年消费金额'];
  int _annualFeeUnit = 4;
  final _annualFeeUnits = ['日', '周', '月', '年'];

  final TextEditingController _annualFeeCountController = TextEditingController(); //刷卡次数
  final TextEditingController _annualFeeAmountController = TextEditingController(); //年消费金额
  final TextEditingController _cardNoController = TextEditingController();
  final TextEditingController _amountController = TextEditingController(text: '0.00'); //额度
  final TextEditingController _billedController = TextEditingController(text: '0.00'); //已出账单
  final TextEditingController _notBilledController = TextEditingController(text: '0.00'); //未出账单
  // final TextEditingController _availableController = TextEditingController(); //可用额度
  final TextEditingController _usedController = TextEditingController(text: '0.00'); //消费金额

  final FocusNode _billedFocusNode = FocusNode();
  final FocusNode _notBilledFocusNode = FocusNode();
  final FocusNode _amountFocusNode = FocusNode();

  final List<_CreditCouponController> _couponInfos = [];

  final ScrollController _scrollController = ScrollController();

  bool _isKeyboardVisible = false;

  late VoidCallback focusCallback = () {
    logger.i('AccountAddCredit foucs changed ${_billedFocusNode.hasFocus} ${_notBilledFocusNode.hasFocus} ${_amountFocusNode.hasFocus}');
    if (_billedFocusNode.hasFocus) {
      _billedController.text = '';
    } else if (_billedController.text.isEmpty) {
      _billedController.text = '0.00';
    }
    if (_notBilledFocusNode.hasFocus) {
      _notBilledController.text = '';
    } else if (_notBilledController.text.isEmpty) {
      _notBilledController.text = '0.00';
    }
    if (_amountFocusNode.hasFocus) {
      _amountController.text = '';
    } else if (_amountController.text.isEmpty) {
      _amountController.text = '0.00';
    }
  };

  @override
  void initState() {
    widget.addController?.onAction = _addAccount;
    FocusManager.instance.addListener(focusCallback);
    WidgetsBinding.instance.addObserver(this);
    _initData();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    FocusManager.instance.removeListener(focusCallback);
    WidgetsBinding.instance.removeObserver(this);
    _billedFocusNode.dispose();
    _notBilledFocusNode.dispose();
    _amountFocusNode.dispose();
    _notBilledController.dispose();
    _amountController.dispose();
    _annualFeeCountController.dispose();
    _annualFeeAmountController.dispose();
    _cardNoController.dispose();
    _billedController.dispose();
  }

  @override
  void didChangeMetrics() {
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;
    final newValue = bottomInset > 0.0;
    if (newValue != _isKeyboardVisible) {
      _isKeyboardVisible = newValue;
      // if (!_isKeyboardVisible) {
      //   priceFocusNode.unfocus();
      //   quantityFocusNode.unfocus();
      // }
    }
  }

  void _initData() {
    if (widget.accountModel != null) {
      _selectedBankItem = BankItem(widget.accountModel!.bankId, widget.accountModel!.bankName, widget.accountModel!.bankIcon, null);
      _amountController.text = widget.accountModel!.balance ?? '';
      _cardNoController.text = widget.accountModel!.accountName ?? '';
      _annualFeeDate = _dateFormat.tryParse(widget.accountModel!.annualFeeDate ?? '');
      _billingDate = int.tryParse(widget.accountModel!.billDate ?? '');
      _paymentDate = int.tryParse(widget.accountModel!.repaymentDate ?? '');
      _billedController.text = widget.accountModel!.billAmount ?? '';
      _notBilledController.text = widget.accountModel!.notBilledAmount ?? '';
      // _availableController.text = widget.accountModel!.availableAmount ?? '';
      _usedController.text = widget.accountModel!.consumptionAmount ?? '';
      _inAssets = widget.accountModel!.isJoinTotal == 1;
      if (widget.accountModel!.annualFeeCondition != null) {
        _annualFeeType = int.tryParse(widget.accountModel!.annualFeeCondition!.conditionType ?? '1') ?? 1;
        _annualFeeUnit = int.tryParse(widget.accountModel!.annualFeeCondition!.countTimeUnit ?? '4') ?? 4;
        _annualFeeCountController.text = widget.accountModel?.annualFeeCondition?.count ?? '';
        _annualFeeAmountController.text = widget.accountModel?.annualFeeCondition?.amount ?? '';
      }
      if (widget.accountModel!.promotions?.isNotEmpty == true) {
        for (var item in widget.accountModel!.promotions!) {
          var coupon = _CreditCouponController();
          coupon.condition = item;
          _couponInfos.add(coupon);
        }
      }
    }
  }

  Future<void> _addAccount() async {
    if (_selectedBankItem == null) {
      showToast('请先选择银行');
      return;
    }
    if (_cardNoController.text.isEmpty) {
      showToast('请填写卡号后4位');
      return;
    }
    if (_amountController.text.isEmpty) {
      showToast('请填写额度');
      return;
    }
    if (_annualFeeDate == null) {
      showToast('请填写年费日');
      return;
    }
    if (_billingDate == null || _paymentDate == null) {
      showToast('请填写账单日和还款日');
      return;
    }
    if (_annualFeeType == 1) {
      if (_annualFeeCountController.text.isEmpty) {
        showToast('请输入免年费条件');
        return;
      }
    } else if (_annualFeeType == 2) {
      if (_annualFeeAmountController.text.isEmpty) {
        showToast('请输入免年费条件');
        return;
      }
    }

    List<PromotionCondition> promotions = [];

    for (var i = 0; i < _couponInfos.length; i++) {
      PromotionCondition? condition = _couponInfos[i].collectCouponInfo?.call();
      if (condition != null) {
        if (condition.promotionName.isEmpty) {
          showToast('填写优惠活动名称');
          return;
        }
        if (condition.conditionType == '1' || condition.conditionType == '3') {
          if (condition.count == null || condition.count == '0') {
            showToast('填写优惠活动条件');
            return;
          }
        } else if (condition.conditionType == '2') {
          if (condition.amount?.isNotEmpty == false) {
            showToast('填写优惠活动消费金额');
            return;
          }
        }
        promotions.add(condition);
      } else {
        showToast('请填写完整的优惠活动');
        return;
      }
    }

    if (_annualFeeType == 1) {
      if (_annualFeeCountController.text.isEmpty) {
        showToast('请填写免年费条件');
        return;
      }
    } else if (_annualFeeType == 2) {
      if (_annualFeeAmountController.text.isEmpty) {
        showToast('请填写免年费条件');
        return;
      }
    }

    AnnualFeeCondition annualFeeCondition = AnnualFeeCondition('$_annualFeeType', _annualFeeType == 1 ? _annualFeeCountController.text : null,
        _annualFeeType == 1 ? '$_annualFeeUnit' : null, _annualFeeType == 2 ? _annualFeeAmountController.text : null);

    Loading.show();
    try {
      var resp = await AccountRepo.addSavingCard(
          id: widget.accountModel?.id,
          accountType: '2',
          bankId: '${_selectedBankItem?.id}',
          cardNo: _cardNoController.text,
          annualFeeCondition: annualFeeCondition,
          quota: _amountController.text,
          isJoinTotal: _inAssets ? '1' : '2',
          repaymentDate: '$_paymentDate',
          issuedBill: _billedController.text,
          noissuedBill: _notBilledController.text,
          // availableAmount: _availableController.text,
          consumptionAmount: _usedController.text,
          promotionConditions: promotions,
          billDate: '$_billingDate',
          annualFee: _dateFormat.format(_annualFeeDate!));
      if (resp.code == 1) {
        showToast('添加账号成功');
        RouterHelper.router.pop();
      } else {
        showToast(resp.msg ?? '添加账号失败');
      }
    } catch (e) {
      showToast('添加账号失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return SingleChildScrollView(
      controller: _scrollController,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _bankSection,
          const SizedBox(
            height: 14,
          ),
          _detailSection,
          const SizedBox(
            height: 14,
          ),
          // _sectionHeader('优惠活动'),
          SectionHeader(
            '优惠活动',
            trailingStr: '+增加优惠活动',
            trailingColor: MColor.xFFCB322E,
            onTrailingTap: () async {
              setState(() {
                _couponInfos.add(_CreditCouponController());
              });
              // await Future.delayed(Duration(seconds: 2));
              // WidgetsBinding.instance.addPostFrameCallback((_) {
              //   _scrollController.animateTo(
              //     _scrollController.position.maxScrollExtent,
              //     duration: Duration(seconds: 1),
              //     curve: Curves.ease,
              //   );
              // });
            },
          ),
          _couponSection,

          const SizedBox(
            height: 14,
          ),
        ],
      ),
    );
  }

  Widget get _couponSection {
    if (_couponInfos.isEmpty) {
      return const SizedBox();
    }
    List<Widget> couponViews = List.generate(_couponInfos.length, (index) {
      return Container(
          margin: EdgeInsets.symmetric(vertical: 14),
          padding: EdgeInsets.symmetric(
            horizontal: 14,
          ),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
          child: _CreditCouponView(_couponInfos[index]));
    });
    return Column(
      children: [
        ...couponViews,
        InkWell(
          onTap: () {
            _couponInfos.removeLast();
            setState(() {});
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.delete_outline, color: MColor.xFFCB322E, size: 18),
              const SizedBox(
                width: 4,
              ),
              Text('删除活动', style: TextStyle(height: 1.4, fontSize: 13, color: MColor.xFFCB322E, fontWeight: FontWeight.w500))
            ],
          ),
        )
      ],
    );
  }

  Widget get _bankSection {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        RouterHelper.router.pushNamed(Routes.bankListPath, extra: {'selectMode': true}).then((value) {
          if (value is BankItem) {
            _selectedBankItem = value;
            setState(() {});
          }
        });
      },
      child: Container(
        // margin: EdgeInsets.symmetric(horizontal: 14),
        padding: EdgeInsets.symmetric(horizontal: 14, vertical: 12),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
        child: Row(
          children: [
            if (_selectedBankItem == null) ...{
              Text(
                '请选择银行',
                style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
              )
            } else ...{
              RoundImage(imageUrl: getImageUrl(_selectedBankItem!.bankImage ?? ''), radius: 16, size: 32),
              const SizedBox(
                width: 10,
              ),
              Text(
                _selectedBankItem!.bankName ?? '',
                style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
              )
            }
          ],
        ),
      ),
    );
  }

  Widget get _detailSection {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 0),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
      child: Column(
        children: [
          AccountInputTile(
            leadingText: '账户名称',
            hint: '6个字符以内，如建白金',
            maxLength: 6,
            textController: _cardNoController,
          ),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          AccountInputTile(
              leadingText: '额度',
              textController: _amountController,
              focusNode: _amountFocusNode,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          AccountDateTile(
            leadingText: '年费日',
            initDate: _annualFeeDate,
            firstDate: DateTime(DateTime.now().year, 1, 1),
            lastDate: DateTime(DateTime.now().year, 12, 31),
            showYear: false,
            onDatePicked: (dt) {
              if (dt != null) {
                setState(
                  () {
                    _annualFeeDate = dt;
                  },
                );
              }
            },
          ),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          AccountDateTile(
            leadingText: '账单日',
            initDate: _billingDate != null ? DateTime(2024, 12, _billingDate!) : null,
            showYear: false,
            showMonth: false,
            onTap: () {
              RouterHelper.router.pushNamed(Routes.popupPath, extra: {'widget': _DayPicker(_billingDate ?? DateTime.now().day)}).then((val) {
                if (val != null && val != _billingDate) {
                  int selectedDay = val as int;
                  _billingDate = selectedDay;
                  setState(() {});
                }
              });
            },
            // onDatePicked: (dt) {
            //   if (dt != null) {
            //     setState(
            //       () {
            //         _billingDate = dt;
            //       },
            //     );
            //   }
            // },
          ),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          AccountDateTile(
            leadingText: '还款日',
            initDate: _paymentDate != null ? DateTime(2024, 12, _paymentDate!) : null,
            showYear: false,
            showMonth: false,
            onTap: () {
              RouterHelper.router.pushNamed(Routes.popupPath, extra: {'widget': _DayPicker(_paymentDate ?? DateTime.now().day)}).then((val) {
                if (val != null && val != _paymentDate) {
                  int selectedDay = val as int;
                  _paymentDate = selectedDay;
                  setState(() {});
                }
              });
            },
          ),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          AccountInputTile(
              leadingText: '已出账单',
              textController: _billedController,
              focusNode: _billedFocusNode,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          AccountInputTile(
              leadingText: '未出账单',
              textController: _notBilledController,
              focusNode: _notBilledFocusNode,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          // AccountInputTile(
          //     leadingText: '可用额度',
          //     textController: _availableController,
          //     keyboardType: TextInputType.numberWithOptions(decimal: true),
          //     inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
          // Divider(
          //   height: 0.5,
          //   thickness: 0.5,
          //   color: MColor.xFFF5F5F5,
          // ),
          Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 14,
                ),
                Text(
                  '免年费条件',
                  style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
                ),
                const SizedBox(
                  height: 14,
                ),
                AccountRadiosTile(
                  choices: _annualFeeStr,
                  selected: _annualFeeType,
                  marginBetween: 16,
                  onSelected: (selected) {
                    setState(() {
                      _annualFeeType = selected;
                    });
                  },
                ),
                if (_annualFeeType == 1) ...{
                  const SizedBox(
                    height: 14,
                  ),
                  Row(
                    children: [
                      Text(
                        '次数',
                        style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF1B1C1A, fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(
                        width: 12,
                      ),
                      SizedBox(
                        width: 70,
                        child: TextField(
                          controller: _annualFeeCountController,
                          style: TextStyle(fontSize: 12, color: MColor.xFF777777, height: 1.4),
                          textAlign: TextAlign.end,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                          decoration: InputDecoration(
                            hintText: '输入数字',
                            hintStyle: TextStyle(fontSize: 12, color: MColor.xFF777777, height: 1.4),
                            border: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide(width: 0.5, color: MColor.xFF999999)),
                            enabledBorder:
                                OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide(width: 0.5, color: MColor.xFF999999)),
                            focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide(width: 0.5, color: MColor.skin)),
                            filled: true,
                            fillColor: MColor.xFFF5F5F5,
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3.5),
                            isDense: true,
                          ),
                          onChanged: (value) {
                            // if (_planStoreRule == null) {
                            //   _planStoreRule = PlanStoreRule();
                            // }
                            // _planStoreRule!.times = value;
                          },
                        ),
                      ),
                      const SizedBox(
                        width: 7,
                      ),
                      Text(
                        '/',
                        style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
                      ),
                      const SizedBox(
                        width: 7,
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 7.5, vertical: 3.5),
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(4), border: Border.all(color: MColor.xFF999999, width: 0.5)),
                        child: GestureDetector(
                          onTap: () {
                            List<Widget> actions = List.generate(_annualFeeUnits.length, (index) {
                              return CupertinoActionSheetAction(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  _annualFeeUnit = index + 1;
                                  setState(() {});
                                },
                                child: Text(
                                  _annualFeeUnits[index],
                                  style: TextStyle(fontSize: 16.0, color: MColor.xFF333333),
                                ),
                              );
                            });
                            showCupertinoModalPopup(
                                context: context,
                                builder: (context) {
                                  return CupertinoActionSheet(
                                    cancelButton: CupertinoActionSheetAction(
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: Text('取消', style: TextStyle(fontSize: 16.0, color: MColor.xFF333333, fontWeight: FontWeight.bold)),
                                    ),
                                    actions: actions,
                                  );
                                });
                          },
                          child: Row(
                            children: [
                              Text(
                                _annualFeeUnits[_annualFeeUnit - 1],
                                style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.w500, color: MColor.xFF999999),
                              ),
                              Icon(
                                Icons.keyboard_arrow_down,
                                size: 12,
                                color: MColor.xFF999999,
                              )
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 14,
                  ),
                } else if (_annualFeeType == 2) ...{
                  AccountInputTile(
                      leadingText: '年消费金额',
                      textController: _annualFeeAmountController,
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
                  const SizedBox(
                    height: 4,
                  ),
                },
              ],
            ),
          ),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          Container(
            height: 50,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
            child: AccountSwitchTile(
              leadingText: '是否计入资产',
              initValue: _inAssets,
              onChanged: (v) {
                setState(() {
                  _inAssets = v;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class _CreditCouponController {
  PromotionCondition? condition;
  PromotionCondition Function()? collectCouponInfo;
}

class _CreditCouponView extends StatefulWidget {
  final _CreditCouponController controller;

  _CreditCouponView(this.controller);

  @override
  State<StatefulWidget> createState() => _CreditCouponState();
}

class _CreditCouponState extends State<_CreditCouponView> {
  int _conditionType = 1;

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _countController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();

  int _timesUnit = 1;
  final _timeCycles = ['日', '周', '月', '年'];

  List<String> _conditions = ['刷卡次数', '消费金额', '消费金额达标次数'];

  @override
  void initState() {
    widget.controller.collectCouponInfo = _collectCouponInfo;
    if (widget.controller.condition != null) {
      _nameController.text = widget.controller.condition!.promotionName ?? '';
      _countController.text = widget.controller.condition!.count ?? '';
      _amountController.text = widget.controller.condition!.amount ?? '';
      _conditionType = int.tryParse(widget.controller.condition!.conditionType ?? '1') ?? 1;
      _timesUnit = int.tryParse(widget.controller.condition!.countTimeUnit ?? '1') ?? 1;
    }

    super.initState();
  }

  PromotionCondition _collectCouponInfo() {
    return PromotionCondition('$_conditionType', _nameController.text, _countController.text, '$_timesUnit', _amountController.text);
  }

  @override
  Widget build(BuildContext context) {
    return _conditionView;
  }

  Widget get _conditionView {
    List<Widget> conditionViews = [];
    List.generate(_conditions.length, (index) {
      conditionViews.add(GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          setState(() {
            _conditionType = index + 1;
          });
        },
        child: Row(
          children: [
            Icon(_conditionType == index + 1 ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                size: 16, color: _conditionType == index + 1 ? MColor.skin : MColor.xFF999999),
            const SizedBox(
              width: 6,
            ),
            Text(
              _conditions[index],
              style: TextStyle(height: 1.4, fontSize: 14, color: _conditionType == index + 1 ? MColor.xFF333333 : MColor.xFF999999),
            ),
          ],
        ),
      ));
      if (index != _conditions.length - 1) {
        conditionViews.add(const Spacer());
      }
    });
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AccountInputTile(leadingText: '优惠名称', textController: _nameController),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          const SizedBox(
            height: 14,
          ),
          Text(
            '优惠条件',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          const SizedBox(
            height: 14,
          ),
          AccountRadiosTile(
            choices: _conditions,
            selected: _conditionType,
            onSelected: (selected) {
              setState(() {
                _conditionType = selected;
              });
            },
          ),
          const SizedBox(
            height: 14,
          ),
          if (_conditionType == 1) ...{
            Row(
              children: [
                Text(
                  '次数',
                  style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF1B1C1A, fontWeight: FontWeight.w500),
                ),
                const SizedBox(
                  width: 12,
                ),
                SizedBox(
                  width: 70,
                  child: TextField(
                    controller: _countController,
                    style: TextStyle(fontSize: 12, color: MColor.xFF777777, height: 1.4),
                    textAlign: TextAlign.end,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                    decoration: InputDecoration(
                      hintText: '输入数字',
                      hintStyle: TextStyle(fontSize: 12, color: MColor.xFF777777, height: 1.4),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide(width: 0.5, color: MColor.xFF999999)),
                      enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide(width: 0.5, color: MColor.xFF999999)),
                      focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide(width: 0.5, color: MColor.skin)),
                      filled: true,
                      fillColor: MColor.xFFF5F5F5,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3.5),
                      isDense: true,
                    ),
                    onChanged: (value) {
                      // if (_planStoreRule == null) {
                      //   _planStoreRule = PlanStoreRule();
                      // }
                      // _planStoreRule!.times = value;
                    },
                  ),
                ),
                const SizedBox(
                  width: 7,
                ),
                Text(
                  '/',
                  style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
                ),
                const SizedBox(
                  width: 7,
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 7.5, vertical: 3.5),
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(4), border: Border.all(color: MColor.xFF999999, width: 0.5)),
                  child: GestureDetector(
                    onTap: () {
                      List<Widget> actions = List.generate(_timeCycles.length, (index) {
                        return CupertinoActionSheetAction(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _timesUnit = index + 1;
                            setState(() {});
                          },
                          child: Text(
                            _timeCycles[index],
                            style: TextStyle(fontSize: 16.0, color: MColor.xFF333333),
                          ),
                        );
                      });
                      showCupertinoModalPopup(
                          context: context,
                          builder: (context) {
                            return CupertinoActionSheet(
                              cancelButton: CupertinoActionSheetAction(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                child: Text('取消', style: TextStyle(fontSize: 16.0, color: MColor.xFF333333, fontWeight: FontWeight.bold)),
                              ),
                              actions: actions,
                            );
                          });
                    },
                    child: Row(
                      children: [
                        Text(
                          _timeCycles[_timesUnit - 1],
                          style: TextStyle(height: 1.3, fontSize: 12, color: MColor.xFF999999),
                        ),
                        Icon(
                          Icons.keyboard_arrow_down,
                          size: 12,
                          color: MColor.xFF999999,
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          } else if (_conditionType == 2) ...{
            AccountInputTile(
                leadingText: '消费金额',
                textController: _amountController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
          } else if (_conditionType == 3) ...{
            AccountInputTile(
                leadingText: '消费金额',
                textController: _amountController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
            Divider(
              height: 0.5,
              thickness: 0.5,
              color: MColor.xFFF5F5F5,
            ),
            AccountInputTile(
                leadingText: '达标次数',
                textController: _countController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))]),
          },
          const SizedBox(
            height: 14,
          ),
        ],
      ),
    );
  }
}

class _DayPicker extends StatelessWidget {
  final int pickedDay;

  const _DayPicker(this.pickedDay);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  '选择日期',
                  textAlign: TextAlign.center,
                  style: TextStyle(height: 1.3, fontSize: 16, color: MColor.xFF1B1C1A, fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              const SizedBox(
                width: 14,
              ),
              Expanded(
                child: GridView.count(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    crossAxisCount: 5,
                    // controller: _scrollController,
                    physics: const ClampingScrollPhysics(),
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(horizontal: 14),
                    childAspectRatio: 1.2,
                    crossAxisSpacing: 4,
                    mainAxisSpacing: 4,
                    children: List.generate(31, (index) {
                      bool isPicked = ((index + 1) == pickedDay);
                      return GestureDetector(
                        onTap: () {
                          RouterHelper.router.pop(index + 1);
                        },
                        child: Container(
                            width: 40,
                            height: 28,
                            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                            decoration: BoxDecoration(
                                color: Colors.transparent,
                                border: Border.all(width: 1, color: isPicked ? MColor.skin : MColor.xFFE8E8E8),
                                borderRadius: BorderRadius.circular(15)),
                            child: Center(
                              child: Text(
                                '${index + 1}',
                                style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                              ),
                            )),
                      );
                    })),
                // Wrap(
                //   direction: Axis.horizontal,
                //   alignment: WrapAlignment.start,
                //   spacing: 14.0,
                //   runSpacing: 8.0,
                //   children: List.generate(31, (index) {
                //     return GestureDetector(
                //       onTap: () {},
                //       child: Container(
                //           width: 40,
                //           height: 28,
                //           padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                //           decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(15)),
                //           child: Text(
                //             '${index + 1}',
                //             style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                //           )),
                //     );
                //   }),
                // ),
              ),
              const SizedBox(
                width: 14,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
