import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_add_big_assets_section.dart';
import 'package:qiazhun/modules/account/account_add_cash_section.dart';
import 'package:qiazhun/modules/account/account_add_credit_section.dart';
import 'package:qiazhun/modules/account/account_add_donation_section.dart';
import 'package:qiazhun/modules/account/account_add_internet_account_section.dart';
import 'package:qiazhun/modules/account/account_add_investing_section.dart';
import 'package:qiazhun/modules/account/account_add_loan_section.dart';
import 'package:qiazhun/modules/account/account_add_saving_section.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class AccountAddController {
  Future<void> Function()? onAction;
}

class AccountAddPage extends StatefulWidget {
  final int? accountId;
  final int? initialTab;
  final int? initialSubTab;
  const AccountAddPage({this.accountId, this.initialTab, this.initialSubTab, super.key});

  @override
  State<StatefulWidget> createState() => _AccountAddState();
}

class _AccountAddState extends State<AccountAddPage> with SingleTickerProviderStateMixin {
  int _selectedType = 0;
  late TabController _tabController;

  final AccountAddController _addController = AccountAddController();

  final List<Map<String, dynamic>> _accountItems = [
    {
      'icon': 'assets/images/ic_credit_card.png',
      'selectedIcon': 'assets/images/ic_credit_card_selected.png',
      'label': '信用卡',
    },
    {
      'icon': 'assets/images/ic_saving_card.png',
      'selectedIcon': 'assets/images/ic_saving_card_selected.png',
      'label': '储蓄卡',
    },
    {
      'icon': 'assets/images/ic_internet_account.png',
      'selectedIcon': 'assets/images/ic_internet_account_selected.png',
      'label': '网络账号',
    },
    {'icon': 'assets/images/ic_cash_account.png', 'selectedIcon': 'assets/images/ic_cash_account_selected.png', 'label': '现金账户'},
    {'icon': 'assets/images/ic_investing_account.png', 'selectedIcon': 'assets/images/ic_investing_account_selected.png', 'label': '投资账户'},
    {'icon': 'assets/images/ic_loan_account.png', 'selectedIcon': 'assets/images/ic_loan_account_selected.png', 'label': '借贷往来'},
    {'icon': 'assets/images/ic_donation_account.png', 'selectedIcon': 'assets/images/ic_donation_account_selected.png', 'label': '捐赠账户'},
    {'icon': 'assets/images/ic_big_amount_assets.png', 'selectedIcon': 'assets/images/ic_big_amount_assets_selected.png', 'label': '大笔资产'},
  ];

  @override
  void initState() {
    _tabController = TabController(length: _accountItems.length, vsync: this);
    if (widget.initialTab != null) {
      _selectedType = widget.initialTab!;
      _tabController.animateTo(_selectedType);
    }
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          _selectedType = _tabController.index;
        });
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: Scaffold(
        extendBody: true,
        persistentFooterButtons: [_submitButton],
        body: SafeArea(
          top: false,
          child: Container(
            color: Colors.yellow,
            child: Stack(
              children: [
                Container(
                  color: const Color(0xFFF5F5F5),
                ),
                Positioned(
                  // top: 0,
                  child: Container(
                    height: 203,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                    ),
                  ),
                ),
                Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: AppBar(
                        backgroundColor: Colors.transparent,
                        scrolledUnderElevation: 0,
                        centerTitle: true,
                        leading: IconButton(
                          icon: Image.asset(
                            'assets/images/ic_back.png',
                            width: 24,
                            height: 24,
                          ),
                          onPressed: () {
                            RouterHelper.router.pop();
                          },
                        ),
                        titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        title: Text(
                          '添加账号',
                          style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        ))),
                Positioned.fill(
                    top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                    // top: 0,
                    child: _accountTypeSection)
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget get _accountTypeSection {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 14.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DefaultTabController(
            length: _accountItems.length,
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              dividerColor: Colors.transparent,
              indicatorColor: Colors.transparent,
              labelPadding: EdgeInsets.symmetric(horizontal: 8),
              tabs: List.generate(_accountItems.length, (index) {
                bool isSelected = _selectedType == index;
                return Tab(
                  child: Container(
                    decoration: isSelected
                        ? BoxDecoration(
                            color: MColor.xFF1B1C1A,
                            borderRadius: BorderRadius.circular(20),
                          )
                        : null,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          isSelected ? _accountItems[index]['selectedIcon'] : _accountItems[index]['icon'],
                          width: 24,
                          height: 24,
                          fit: BoxFit.fill,
                        ),
                        SizedBox(width: 4),
                        Text(
                          _accountItems[index]['label'],
                          style: TextStyle(
                            fontSize: 13,
                            height: 1.4,
                            color: isSelected ? Colors.white : MColor.xFF1B1C1A,
                          ),
                        ),
                        SizedBox(width: 4),
                      ],
                    ),
                  ),
                );
              }),
            ),
          ),
          const SizedBox(height: 18),
          _sectionHeader('填写账号信息'),
          const SizedBox(height: 14),
          Expanded(child: TabBarView(controller: _tabController, children: _accountTypeContentView))
        ],
      ),
    );
  }

  Widget _sectionHeader(String headerStr) {
    return Row(
      children: [
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.transparent, Colors.transparent, MColor.xFFFED58E, MColor.xFFFED58E],
              stops: [0.0, 0.6, 0.6, 1.0],
              begin: const FractionalOffset(0.0, 0.0),
              end: const FractionalOffset(0.0, 1.0),
            ),
          ),
          child: Text(headerStr, style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
        ),
      ],
    );
  }

  List<Widget> get _accountTypeContentView {
    return [
      AccountAddCreditSection(
        addController: _addController,
      ),
      AccountAddSavingSection(addController: _addController),
      AccountAddInternetAccountSection(addController: _addController),
      AccountAddCashSection(addController: _addController),
      AccountAddInvestingSection(addController: _addController),
      AccountAddLoanSection(
        addController: _addController,
        selectedSubType: widget.initialSubTab,
      ),
      AccountAddDonationSection(addController: _addController),
      AccountAddBigAssetsSection(addController: _addController),
    ];
  }

  Widget get _submitButton {
    return GestureDetector(
      onTap: () {
        _addController.onAction?.call();
      },
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient:
                LinearGradient(begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '提交',
              style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
