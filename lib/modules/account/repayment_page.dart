import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/price_view.dart';
import 'package:qiazhun/widgets/empty_view.dart';

class RepaymentPage extends StatefulWidget {
  final String accountId;
  const RepaymentPage({required this.accountId, super.key});

  @override
  State<StatefulWidget> createState() => _RepaymentPageState();
}

class _RepaymentPageState extends State<RepaymentPage> {
  bool _isLoading = true;
  AccountModel? _accountData;
  AccountModel? _repaymentAccount;
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _feeController = TextEditingController();
  final TextEditingController _memoController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadAccountData();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _feeController.dispose();
    _memoController.dispose();
    super.dispose();
  }

  Future<void> _loadAccountData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await AccountRepo.getAccountDetail(widget.accountId);
      if (response.code == 1 && response.data != null) {
        _accountData = response.data;
        // Set default amount to bill amount for full repayment
        if (_accountData?.billAmount != null) {
          _amountController.text = _accountData!.billAmount!;
        }
      } else {
        showToast(response.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      _isLoading = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(title: '还款', body: _isLoading ? _buildLoadingView() : _buildContent());
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        color: MColor.skin,
      ),
    );
  }

  Widget _buildContent() {
    if (_accountData == null) {
      return const Center(
        child: EmptyView(),
      );
    }

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildAccountCard(),
                const SizedBox(height: 20),
                _buildAmountSection(),
                const SizedBox(height: 20),
                _buildFeeSection(),
                const SizedBox(height: 20),
                _buildFromAccountSection(),
                const SizedBox(height: 20),
                _buildMemoSection(),
              ],
            ),
          ),
        ),
        _buildBottomButton(),
      ],
    );
  }

  Widget _buildAccountCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFED58E), Color(0xFFF6CB86)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.credit_card,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                '${_accountData!.bankName}信用卡',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            '待还金额',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 4),
          PriceView(
            price: PriceInfo.parsePrice(_accountData!.billAmount ?? '0.00'),
            integerFontSize: 32,
            fractionalFontSize: 24,
            textColor: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ],
      ),
    );
  }

  Widget _buildAmountSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '还款金额',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _amountController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
            ],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: MColor.xFF1B1C1A,
            ),
            decoration: InputDecoration(
              hintText: '0.00',
              hintStyle: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF999999,
              ),
              prefixText: '¥ ',
              prefixStyle: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF1B1C1A,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
            enabled: true,
          ),
        ],
      ),
    );
  }

  Widget _buildFromAccountSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '还款账户',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              RouterHelper.router.pushNamed(
                Routes.accountListPath,
                extra: {
                  'selectMode': true,
                  'limitAccount': '8',
                },
              ).then((value) {
                if (value != null && value is Map<String, dynamic> && value.containsKey('selected')) {
                  _repaymentAccount = value['selected'] as AccountModel;
                  setState(() {});
                }
              });
            },
            child: Row(
              children: [
                const Icon(
                  Icons.credit_card,
                  color: Colors.grey,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _repaymentAccount?.getShortDesc() ?? '选择还款账户',
                    style: TextStyle(
                      color: MColor.xFF1B1C1A,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _repaymentAccount = null;
                    });
                  },
                  child: Icon(
                    _repaymentAccount != null ? Icons.close : Icons.arrow_forward_ios,
                    color: MColor.xFF999999,
                    size: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeeSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '手续费',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _feeController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
            ],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: MColor.xFF1B1C1A,
            ),
            decoration: InputDecoration(
              hintText: '0.00',
              hintStyle: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF999999,
              ),
              prefixText: '¥ ',
              prefixStyle: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF1B1C1A,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
            enabled: true,
          ),
        ],
      ),
    );
  }

  Widget _buildMemoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '备注',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _memoController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: '添加备注信息（可选）',
              hintStyle: TextStyle(
                color: MColor.xFF999999,
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFF0F0F0), width: 1),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _handleRepayment,
            style: ElevatedButton.styleFrom(
              backgroundColor: MColor.skin,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              '确认还款',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleRepayment() {
    final amount = _amountController.text.trim();
    if (amount.isEmpty || double.tryParse(amount) == null || double.parse(amount) <= 0) {
      showToast('请输入有效的还款金额');
      return;
    }
    if (_repaymentAccount?.id == null) {
      showToast('请选择还款账户');
      return;
    }

    try {
      AccountRepo.repayment(
          accountId: widget.accountId,
          repaymentAccountId: '${_repaymentAccount?.id}',
          repaymentPrice: amount,
          handlingFees: _feeController.text.trim().isEmpty ? '0.00' : _feeController.text.trim(),
          memo: _memoController.text);
      showToast('还款成功');
      RouterHelper.router.pop();
    } catch (e) {
      showToast('还款失败 $e');
    } finally {
      Loading.dismiss();
    }
  }
}
