import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class BankListPage extends StatefulWidget {
  final bool isSelectMode;
  const BankListPage({required this.isSelectMode, super.key});
  @override
  State<StatefulWidget> createState() => _BankListState();
}

class _BankListState extends State<BankListPage> {
  final List<BankItem> _bankList = [];

  @override
  void initState() {
    _initData();
    super.initState();
  }

  Future<void> _initData() async {
    Loading.show();
    try {
      var resp = await AccountRepo.getBankList();
      if (resp.code == 1) {
        _bankList.clear();
        _bankList.addAll(resp.data ?? []);
        setState(() {});
      } else {
        showToast(resp.msg ?? '获取银行列表失败');
      }
    } catch (e) {
      showToast('获取银行列表失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
          backgroundColor: Colors.transparent,
          scrolledUnderElevation: 0,
          centerTitle: true,
          leading: IconButton(
            icon: Image.asset(
              'assets/images/ic_back.png',
              width: 24,
              height: 24,
            ),
            onPressed: () {
              RouterHelper.router.pop();
            },
          ),
          title: Image.asset(
            'assets/images/icon_title.png',
            width: 129,
            height: 30,
          )),
      body: SafeArea(
        top: false,
        child: Stack(
          children: [
            Container(
              color: Color(0xFFF5F5F5),
            ),
            Positioned(
              // top: 0,
              child: Container(
                height: 203,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                ),
              ),
            ),
            Positioned.fill(
              top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
              child: Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
                padding: EdgeInsets.all(14),
                margin: EdgeInsets.symmetric(horizontal: 14),
                child: ListView.separated(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return _bankItemView(_bankList[index]);
                  },
                  separatorBuilder: (context, index) {
                    return const SizedBox(
                      height: 14,
                    );
                  },
                  itemCount: _bankList.length,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _bankItemView(BankItem item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (widget.isSelectMode) {
          RouterHelper.router.pop(item);
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 9.0),
        child: Row(
          children: [
            RoundImage(imageUrl: getImageUrl(item.bankImage), radius: 16, size: 32),
            const SizedBox(
              width: 10,
            ),
            Text(item.bankName ?? '')
          ],
        ),
      ),
    );
  }
}
