import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/settings/setting_model.dart';
import 'package:qiazhun/modules/settings/setting_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class AddBookkeepingPage extends StatefulWidget {
  final String? bookkeepingNumber;
  const AddBookkeepingPage({this.bookkeepingNumber, super.key});

  @override
  State<StatefulWidget> createState() => _AddLedgerState();
}

class _AddLedgerState extends State<AddBookkeepingPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _remarkController = TextEditingController();

  String? _iconUrl = SettingStore.to.accountIconList.firstOrNull?.icon;

  bool _inAssets = true;

  initState() {
    super.initState();
    if (widget.bookkeepingNumber?.isNotEmpty == true) {
      _loadData();
    }
  }

  Future<void> _loadData() async {
    Loading.show();
    try {
      var resp = await BookkeepingRepo.getBookkeepingDetail(widget.bookkeepingNumber!);
      if (resp.code == 1) {
        resp.data?.accountBookName;
        _nameController.text = resp.data?.accountBookName ?? '';
        _remarkController.text = resp.data?.memo ?? '';
        _inAssets = resp.data?.isJoinTotal == '1';
        _iconUrl = resp.data?.bookkeepingIcon;
        setState(() {});
      } else {
        showToast(resp.msg ?? '获取账本详情失败');
      }
    } catch (e) {
      showToast('获取账本详情失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _remarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: Scaffold(
        extendBody: true,
        resizeToAvoidBottomInset: false,
        persistentFooterButtons: [_submitButton],
        body: SafeArea(
          // maintainBottomViewPadding: true,
          top: false,
          bottom: false,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        widget.bookkeepingNumber?.isNotEmpty == true ? '编辑账本' : '新增账本',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                // top: 0,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14.0),
                  child: Column(
                    children: [_nameSection, _iconSection, _assetSection, _remarkSection],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget get _nameSection {
    return Column(
      children: [
        const SizedBox(
          height: 14,
        ),
        _sectionHeader('账本名称'),
        const SizedBox(
          height: 14,
        ),
        TextField(
          controller: _nameController,
          keyboardType: TextInputType.text,
          decoration: InputDecoration(
            hintText: '请输入',
            hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
            isDense: true,
          ),
        )
      ],
    );
  }

  Widget get _iconSection {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 14,
        ),
        _sectionHeader('账本图标'),
        const SizedBox(
          height: 14,
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            RouterHelper.router.pushNamed(Routes.iconListPath, extra: {'iconList': SettingStore.to.accountIconList}).then((value) {
              if (value != null && value is Map<String, IconInfo> && value.containsKey('result')) {
                setState(() {
                  _iconUrl = value['result']?.icon;
                });
              }
            });
          },
          child: RoundImage(imageUrl: getImageUrl(_iconUrl), radius: 18, size: 36),
          // ClipRRect(
          //   borderRadius: BorderRadius.circular(20),
          //   child: CachedNetworkImage(
          //       imageUrl: getImageUrl(_iconUrl ?? ''),
          //       width: 36,
          //       height: 36,
          //       fit: BoxFit.cover,
          //       placeholder: (ctx, e) {
          //         return Container(
          //           decoration: BoxDecoration(
          //             color: MColor.xFFECECEC,
          //           ),
          //         );
          //       },
          //       errorWidget: (ctx, e, x) {
          //         return Image.asset(
          //           'assets/images/ic_default_ledger.png',
          //           width: 40,
          //           height: 40,
          //           fit: BoxFit.fill,
          //         );
          //       }),
          // ),
        )
      ],
    );
  }

  Widget get _assetSection {
    return Column(
      children: [
        const SizedBox(
          height: 14,
        ),
        _sectionHeader('是否计入总资产'),
        const SizedBox(
          height: 14,
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 14, vertical: 16),
          decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(10)),
          child: Row(
            children: [
              Text(
                '是否计入总资产',
                style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 16, height: 1.125),
              ),
              const Spacer(),
              SizedBox(
                height: 24,
                child: FittedBox(
                  fit: BoxFit.fill,
                  child: Switch(
                    value: _inAssets,
                    activeTrackColor: MColor.skin,
                    activeColor: MColor.xFFFFFFFF,
                    onChanged: (value) {
                      setState(() {
                        _inAssets = !_inAssets;
                      });
                    },
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget get _remarkSection {
    return Column(
      children: [
        const SizedBox(
          height: 14,
        ),
        _sectionHeader('账本备注'),
        const SizedBox(
          height: 14,
        ),
        TextField(
          controller: _remarkController,
          keyboardType: TextInputType.text,
          decoration: InputDecoration(
            hintText: '请输入要备注的内容',
            hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
            isDense: true,
          ),
        )
      ],
    );
  }

  Widget _sectionHeader(String headerStr) {
    return Row(
      children: [
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.transparent, Colors.transparent, MColor.xFFFED58E, MColor.xFFFED58E],
              stops: [0.0, 0.6, 0.6, 1.0],
              begin: const FractionalOffset(0.0, 0.0),
              end: const FractionalOffset(0.0, 1.0),
            ),
          ),
          child: Text(headerStr, style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
        ),
      ],
    );
  }

  Widget get _submitButton {
    return GestureDetector(
      onTap: () {
        _submit();
      },
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient:
                LinearGradient(begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '提交',
              style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submit() async {
    if (_nameController.text.isEmpty || _iconUrl?.isNotEmpty != true) {
      return;
    }
    Loading.show();
    try {
      var resp = await BookkeepingRepo.addBookkeeping(_nameController.text, _inAssets, _remarkController.text, _iconUrl ?? '', widget.bookkeepingNumber);
      if (resp.code == 1) {
        RouterHelper.router.pop();
        BookkeepingStore.to.getBookkeepingList();
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      Loading.dismiss();
    }
  }
}
