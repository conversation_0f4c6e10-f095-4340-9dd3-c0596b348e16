import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class BookkeepingCategoryView extends StatefulWidget {
  final int categoryTabId;
  final List<CategoryItem> categories;
  final CategoryItem? initialSelected;
  final Function(CategoryItem? categoryItem)? onTap;
  final Function()? onSettingTapped;

  const BookkeepingCategoryView({
    super.key,
    required this.categoryTabId,
    required this.categories,
    this.initialSelected,
    this.onTap,
    this.onSettingTapped,
  });

  @override
  State<BookkeepingCategoryView> createState() => _BookkeepingCategoryViewState();
}

class _BookkeepingCategoryViewState extends State<BookkeepingCategoryView> {
  CategoryItem? _selected;
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _selected = widget.initialSelected;
  }

  @override
  void didUpdateWidget(covariant BookkeepingCategoryView oldWidget) {
    _selected = widget.initialSelected;
    _currentPage = 0;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    List<List<Widget>> pages = [];
    List<Widget> currentPage = [];

    double space = (MediaQuery.of(context).size.width - 5 * 70 - 20) / 4;

    bool settingAdded = false;
    // 添加分类按钮
    for (int i = 0; i < widget.categories.length; i++) {
      currentPage.add(_itemView(i, widget.categories[i]));

      // 每10个分类作为一页
      if (currentPage.length == 10 || i == widget.categories.length - 1) {
        // 如果是最后一页，添加设置按钮
        if (i == widget.categories.length - 1) {
          if (currentPage.length < 10) {
            currentPage.add(_settingItemView(widget.categoryTabId));
            settingAdded = true;
          } else {
            pages.add([...currentPage]);
            currentPage = [_settingItemView(widget.categoryTabId)];
            settingAdded = true;
          }
        }
        pages.add([...currentPage]);
        currentPage = [];
      }
    }

    // 如果最后一页没有设置按钮，添加一个新页面放置设置按钮
    if (!settingAdded) {
      pages.add([_settingItemView(widget.categoryTabId)]);
    }

    return SizedBox(
      height: 145,
      child: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                PageView.builder(
                  controller: _pageController,
                  onPageChanged: (page) {
                    setState(() {
                      _currentPage = page;
                    });
                  },
                  itemCount: pages.length,
                  itemBuilder: (context, pageIndex) {
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      child: Wrap(
                        spacing: space,
                        runSpacing: 4,
                        alignment: WrapAlignment.start,
                        children: pages[pageIndex].map((widget) {
                          return SizedBox(
                            width: 70,
                            child: widget,
                          );
                        }).toList(),
                      ),
                    );
                  },
                ),
                if (pages.length > 1)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        pages.length,
                        (index) => Container(
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _currentPage == index ? MColor.skin : MColor.xFFEBEBEB,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 7),
        ],
      ),
    );
  }

  Widget _settingItemView(int tabId) {
    return GestureDetector(
      onTap: widget.onSettingTapped,
      child: Column(
        children: [
          Image.asset('assets/images/ic_setting.png', width: 38, height: 38),
          const SizedBox(height: 3),
          Text(
            '设置',
            style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF1B1C1A),
          ),
          const SizedBox(height: 8)
        ],
      ),
    );
  }

  Widget _itemView(int index, CategoryItem item) {
    return GestureDetector(
      onTap: () {
        if (_selected == item) {
          _selected = null;
        } else {
          _selected = item;
        }
        widget.onTap?.call(_selected);
        setState(() {});
      },
      child: SizedBox(
        width: 62,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: _selected?.bookkeepingCategoryId == item.bookkeepingCategoryId ? MColor.skin : Colors.transparent,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(19),
                color: MColor.xFFECECEC,
              ),
              child: RoundImage(
                imageUrl: item.bookkeepingCategoryIcon ?? '',
                radius: 17,
                size: 34,
              ),
            ),
            const SizedBox(height: 3),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                item.bookkeepingCategoryName ?? '',
                maxLines: 1,
                style: TextStyle(
                  height: 1.4,
                  fontSize: 12,
                  color: _selected?.bookkeepingCategoryId == item.bookkeepingCategoryId ? MColor.skin : MColor.xFF1B1C1A,
                ),
              ),
            ),
            const SizedBox(height: 8)
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}
