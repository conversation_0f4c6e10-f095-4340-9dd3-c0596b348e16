import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';

class BookkeepingController extends GetxController {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textNode = FocusNode();
  DateTime _selectedDay = DateTime.now();
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');
  //账户
  AccountModel? _selectedIncomeAccount;
  AccountModel? _selectedOutcomeAccount;
  //账本
  BookkeepingInfo? _bookkeepingInfo;
  //分类
  CategoryItem? _selectedOutcomeCategory;
  CategoryItem? _selectedIncomeCategory;

  // 快捷账户相关
  BookkeepingContextResp? _bookkeepingContext;
  List<AccountModel> _shortcutIncomeAccounts = [];
  List<AccountModel> _shortcutOutcomeAccounts = [];

  int _selectedTab = 0;
  double? _bookkeepingResult;
  double? _savingResult;
  bool _noNeed = false;
  bool _isSavingInput = false;
  String _remarkStr = '';

  // 横滑组件的项目顺序
  List<_ShortcutItem> _horizontalItems = [
    _ShortcutItem(type: 'date'),
    _ShortcutItem(type: 'unnecessary'),
    _ShortcutItem(type: 'saving'),
    _ShortcutItem(type: 'accounts')
  ];
}
