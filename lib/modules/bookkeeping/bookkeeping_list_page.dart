import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class BookkeepingListPage extends StatefulWidget {
  const BookkeepingListPage({super.key});

  @override
  State<StatefulWidget> createState() => _BookkeepingListState();
}

class _BookkeepingListState extends State<BookkeepingListPage> {
  @override
  void initState() {
    super.initState();
    BookkeepingStore.to.getBookkeepingList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      actions: [
                        IconButton(
                            onPressed: () {
                              RouterHelper.router.pushNamed(Routes.addLedgerPath);
                            },
                            icon: Image.asset(
                              'assets/images/ic_add_3.png',
                              width: 22,
                              height: 22,
                              fit: BoxFit.fill,
                            )),
                      ],
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '账本列表',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: RefreshIndicator(
                      triggerMode: RefreshIndicatorTriggerMode.anywhere,
                      onRefresh: () async {
                        await BookkeepingStore.to.getBookkeepingList();
                      },
                      child: GetBuilder<BookkeepingStore>(builder: (bkStore) {
                        if (bkStore.bookkeepingList.isEmpty) {
                          return EmptyView();
                        }
                        return ReorderableListView.builder(
                            padding: EdgeInsets.zero,
                            itemBuilder: (context, index) {
                              return Column(children: [
                                const SizedBox(
                                  height: 7,
                                ),
                                _itemView(bkStore.bookkeepingList[index]),
                                const SizedBox(
                                  height: 7,
                                ),
                              ], key: ValueKey(bkStore.bookkeepingList[index].bookkeepingId));
                            },
                            // separatorBuilder: (context, index) {
                            //   return const SizedBox(
                            //     height: 14,
                            //   );
                            // },
                            itemCount: bkStore.bookkeepingList.length,
                            onReorder: (oldIndex, newIndex) {
                              logger.i('old $oldIndex new $newIndex');
                              setState(() {
                                if (oldIndex < newIndex) {
                                  newIndex -= 1;
                                }
                                List<String> idList = List.generate(bkStore.bookkeepingList.length, (index) {
                                  return '${bkStore.bookkeepingList[index].bookkeepingId}';
                                });
                                final item = idList.removeAt(oldIndex);
                                idList.insert(newIndex, item);
                                _sortBookkeepings(idList.join(','));
                              });
                            },
                            proxyDecorator: (child, index, animation) {
                              return Material(
                                elevation: 6,
                                color: Colors.white,
                                child: child,
                              );
                            });
                      })))
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _deleteBookkeeping(BookkeepingInfo data) async {
    Loading.show();
    try {
      var resp = await BookkeepingRepo.delBookkeeping(data.bookkeepingNumber ?? '');
      if (resp.code == 1) {
        BookkeepingStore.to.getBookkeepingList();
      }
    } catch (e) {
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _sortBookkeepings(String newIdList) async {
    debugPrint('_sortBookkeepings ${newIdList}');
    Loading.show();
    try {
      var resp = await BookkeepingRepo.sortBookkeeping(newIdList);
      if (resp.code == 1) {
        BookkeepingStore.to.getBookkeepingList();
      }
    } catch (e) {
    } finally {
      Loading.dismiss();
    }
  }

  Widget _itemView(BookkeepingInfo data, {Key? key}) {
    return Slidable(
      key: key,
      // The end action pane is the one at the right or the bottom side.
      endActionPane: ActionPane(
        motion: ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (context) {
              showCustomDialog(
                '确认删除',
                content: '删除后不可恢复，请确认是否删除',
                cancel: true,
                onConfirm: () async {
                  await _deleteBookkeeping(data);
                },
                onCancel: () {},
              );
            },
            backgroundColor: MColor.xFFFF7858,
            foregroundColor: MColor.xFFFFFFFF,
            icon: Icons.delete,
            label: '删除',
          ),
          SlidableAction(
            onPressed: (context) async {
              RouterHelper.router.pushNamed(Routes.addLedgerPath, extra: {'bookkeepingNumber': data.bookkeepingNumber});
            },
            backgroundColor: MColor.xFFFFBE4A,
            foregroundColor: MColor.xFFFFFFFF,
            icon: Icons.edit,
            label: '编辑',
          )
        ],
      ),
      child: GestureDetector(
        onTap: () {
          // 跳转到账本详情页面
          RouterHelper.router.pushNamed(
            Routes.bookkeepingDetailPath,
            extra: {
              'bookkeepingNumber': data.bookkeepingNumber ?? '',
              'bookkeepingName': data.accountBookName ?? '',
            },
          );
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16, horizontal: 11),
          margin: EdgeInsets.symmetric(horizontal: 14),
          decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
          child: Row(
            children: [
              RoundImage(imageUrl: data.bookkeepingIcon ?? '', radius: 18, size: 36),
              const SizedBox(
                width: 6,
              ),
              Expanded(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    data.accountBookName ?? '',
                    style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
                  ),
                  Text(data.accountBookName ?? '', style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF999999))
                ],
              )),
              Text('共${data.billCount ?? 0}条账单', style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFCB322E))
            ],
          ),
        ),
      ),
    );
  }
}
