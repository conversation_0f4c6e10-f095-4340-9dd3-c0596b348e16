import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_state.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store_provider.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/router/router.dart';

import 'package:qiazhun/tools/tools.dart';

// 扩展方法来替代firstWhereOrNull
extension ListExtension<T> on List<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    try {
      return firstWhere(test);
    } catch (e) {
      return null;
    }
  }
}

// BookkeepingNotifier 来管理记账页面状态
class BookkeepingNotifier extends StateNotifier<BookkeepingState> {
  final TextEditingController textController = TextEditingController();
  final FocusNode textNode = FocusNode();
  final DateFormat dateFormat = DateFormat('yyyy-MM-dd');
  final Ref ref;

  BookkeepingNotifier(this.ref)
      : super(BookkeepingState(
          horizontalItems: [ShortcutItem(type: 'date'), ShortcutItem(type: 'unnecessary'), ShortcutItem(type: 'saving'), ShortcutItem(type: 'accounts')],
        )) {
    // 监听文本输入变化
    textController.addListener(() {
      state = state.copyWith(textInput: textController.text);
    });
  }

  @override
  void dispose() {
    textController.dispose();
    textNode.dispose();
    super.dispose();
  }

  // 初始化数据
  Future<void> initializeData({dynamic logId, String? targetAccountId}) async {
    state = state.copyWith(isLoading: true);

    // 设置初始账本信息
    if (UserStore.to.lastIndexBookkeepingNumber?.isNotEmpty == true) {
      final bookkeepingList = ref.read(bookkeepingListProvider);
      final bookkeepingInfo = bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == UserStore.to.lastIndexBookkeepingNumber);
      state = state.copyWith(bookkeepingInfo: bookkeepingInfo);
    }

    await loadData(requestDetail: logId != null, logId: logId, targetAccountId: targetAccountId);
  }

  // 加载数据
  Future<void> loadData({bool? requestDetail, dynamic logId, String? targetAccountId}) async {
    try {
      // 使用Riverpod store来加载数据
      final storeNotifier = ref.read(bookkeepingStoreProvider.notifier);
      List<Future<dynamic>> requests = [
        storeNotifier.getMyBookkeepingCategory(),
        storeNotifier.getBookkeepingList(),
        storeNotifier.getAccountList(),
        BookkeepingRepo.getBookkeepingContext(),
      ];

      BillDetailInfo? detail;
      if (requestDetail == true) {
        requests.add(BookkeepingRepo.billDetail(logId));
      }

      var resp = await Future.wait(requests);

      // 处理记账上下文响应
      BaseModel<BookkeepingContextResp> contextResp = resp[3] as BaseModel<BookkeepingContextResp>;
      if (contextResp.code == 1) {
        state = state.copyWith(
          bookkeepingContext: contextResp.data,
          shortcutIncomeAccounts: contextResp.data?.shortcutAccounts?.income ?? [],
          shortcutOutcomeAccounts: contextResp.data?.shortcutAccounts?.outcome ?? [],
        );
      }

      // 处理详情数据
      if (requestDetail == true) {
        BaseModel<BillDetailInfo> bm = resp[requests.length - 1] as BaseModel<BillDetailInfo>;
        if (bm.code == 1) {
          detail = bm.data;
        } else {
          showToast('获取流水信息失败');
        }
      }

      if (detail != null) {
        _handleDetailData(detail);
      }

      _setDefaultSelections(targetAccountId);
    } catch (e) {
      logger.e('loadData error: $e');
      showToast('加载数据失败');
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  // 处理详情数据
  void _handleDetailData(BillDetailInfo detail) {
    final bookkeepingList = ref.read(bookkeepingListProvider);
    final bookkeepingInfo = bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == detail.bookkeepingNumber);

    final selectedTab = detail.type == '2' ? 0 : 1;

    AccountModel? selectedIncomeAccount;
    AccountModel? selectedOutcomeAccount;
    CategoryItem? selectedOutcomeCategory;
    CategoryItem? selectedIncomeCategory;

    final outcomeCategories = ref.read(outcomeCategoriesProvider);
    final incomeCategories = ref.read(incomeCategoriesProvider);
    final accountList = ref.read(accountListProvider);

    if (selectedTab == 0) {
      selectedOutcomeCategory = outcomeCategories.firstWhereOrNull((el) => el.bookkeepingCategoryId == detail.categoryId);
      selectedOutcomeAccount = accountList.firstWhereOrNull((el) => el.id == detail.accountId);
    } else {
      selectedIncomeCategory = incomeCategories.firstWhereOrNull((el) => el.bookkeepingCategoryId == detail.categoryId);
      selectedIncomeAccount = accountList.firstWhereOrNull((el) => el.id == detail.accountId);
    }

    final selectedDay = dateFormat.tryParse(detail.date ?? '') ?? DateTime.now();
    final bookkeepingResult = (NumberFormat().tryParse(detail.money ?? '') ?? 0.0).toDouble();
    final savingResult = selectedTab == 0 ? (detail.couponPrice != null ? NumberFormat().tryParse(detail.couponPrice!)?.toDouble() : null) : null;

    state = state.copyWith(
      bookkeepingInfo: bookkeepingInfo,
      selectedTab: selectedTab,
      selectedOutcomeCategory: selectedOutcomeCategory,
      selectedOutcomeAccount: selectedOutcomeAccount,
      selectedIncomeCategory: selectedIncomeCategory,
      selectedIncomeAccount: selectedIncomeAccount,
      selectedDay: selectedDay,
      bookkeepingResult: bookkeepingResult,
      savingResult: savingResult,
      remarkStr: detail.memo ?? '',
      noNeed: detail.isNecessaryStatus == '2',
      isSavingInput: false,
    );

    textController.text = bookkeepingResult.toStringAsFixed(2);
  }

  // 设置默认选择
  void _setDefaultSelections(String? targetAccountId) {
    final bookkeepingList = ref.read(bookkeepingListProvider);
    final outcomeCategories = ref.read(outcomeCategoriesProvider);
    final incomeCategories = ref.read(incomeCategoriesProvider);
    final accountList = ref.read(accountListProvider);

    // 设置默认账本
    if (state.bookkeepingInfo == null) {
      if (UserStore.to.lastIndexBookkeepingNumber?.isNotEmpty == true) {
        final bookkeepingInfo = bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == UserStore.to.lastIndexBookkeepingNumber);
        state = state.copyWith(bookkeepingInfo: bookkeepingInfo);
      }
    }

    // 设置默认分类
    final selectedOutcomeCategory = state.selectedOutcomeCategory ?? (outcomeCategories.isNotEmpty ? outcomeCategories[0] : null);
    final selectedIncomeCategory = state.selectedIncomeCategory ?? (incomeCategories.isNotEmpty ? incomeCategories[0] : null);

    // 设置目标账户
    AccountModel? selectedIncomeAccount = state.selectedIncomeAccount;
    AccountModel? selectedOutcomeAccount = state.selectedOutcomeAccount;

    if (targetAccountId != null) {
      selectedIncomeAccount = accountList.firstWhereOrNull((el) => '${el.id}' == targetAccountId);
      selectedOutcomeAccount = accountList.firstWhereOrNull((el) => '${el.id}' == targetAccountId);
    }

    // 设置默认账户
    selectedIncomeAccount ??= accountList.firstWhereOrNull((el) => el.lastSelectedIncome == 2);
    selectedOutcomeAccount ??= accountList.firstWhereOrNull((el) => el.lastSelectedPay == 2);

    state = state.copyWith(
      selectedOutcomeCategory: selectedOutcomeCategory,
      selectedIncomeCategory: selectedIncomeCategory,
      selectedIncomeAccount: selectedIncomeAccount,
      selectedOutcomeAccount: selectedOutcomeAccount,
    );
  }

  // 添加文本
  void appendText(String append) {
    if (textNode.hasFocus) {
      String preText = textController.text;
      int start = textController.selection.baseOffset;
      int end = textController.selection.extentOffset;
      String newText = '';
      if (start != end) {
        newText = preText.replaceRange(start, end, append);
      } else {
        newText = preText.substring(0, start) + append + preText.substring(start, preText.length);
      }
      textController.value = TextEditingValue(text: newText, selection: TextSelection(baseOffset: start + 1, extentOffset: start + 1));
      textNode.unfocus();
    } else {
      textController.text += append;
    }
  }

  // 删除文本
  void deleteText() {
    String preText = textController.text;
    String newText = '';
    if (textNode.hasFocus) {
      int start = textController.selection.baseOffset;
      int end = textController.selection.extentOffset;
      if (start != end) {
        newText = preText.replaceRange(start, end, '');
      } else if (start == 0) {
        newText = preText;
      } else {
        newText = preText.substring(0, start - 1) + preText.substring(start, preText.length);
      }
    } else {
      if (preText.isNotEmpty) {
        newText = preText.substring(0, preText.length - 1);
      } else {
        newText = '';
      }
    }
    textController.value = TextEditingValue(text: newText);
    textNode.unfocus();
  }

  // 计算表达式
  List _calculate(String userInput) {
    try {
      String input = userInput.replaceAll('×', '*');
      input = input.replaceAll('÷', '/');
      Parser p = Parser(); // TODO: Update to GrammarParser or ShuntingYardParser when available
      Expression expression = p.parse(input);
      ContextModel contextModel = ContextModel();

      double eval = expression.evaluate(EvaluationType.REAL, contextModel);
      return [true, eval];
    } catch (e) {
      showToast('输入错误');
      return [false, null];
    }
  }

  // 确认输入
  void confirm(bool needPop) {
    if (textController.text.isNotEmpty) {
      var [isSuccess, result] = _calculate(textController.text);
      if (isSuccess) {
        if (state.isSavingInput) {
          state = state.copyWith(savingResult: result);
        } else {
          state = state.copyWith(bookkeepingResult: result);
        }
        doRequest(needPop: needPop);
      }
    } else {
      doRequest(needPop: needPop);
    }
  }

  // 提交请求
  Future<void> doRequest({bool? needPop = false, dynamic logId}) async {
    if (state.bookkeepingInfo?.bookkeepingNumber?.isNotEmpty != true) {
      showToast('请先选择账本');
      return;
    }
    if ((state.selectedTab == 0 && state.selectedOutcomeCategory?.bookkeepingCategoryId == null) ||
        (state.selectedTab == 1 && state.selectedIncomeCategory?.bookkeepingCategoryId == null)) {
      showToast('请先选择分类');
      return;
    }
    var selectedAccount = state.selectedTab == 0 ? state.selectedOutcomeAccount : state.selectedIncomeAccount;
    if (selectedAccount?.id == null) {
      showToast('请先选择账户');
      return;
    }
    if (state.bookkeepingResult == null) {
      showToast('请先输入金额');
      return;
    }
    if (state.isSavingInput && state.savingResult == null) {
      showToast('请先输入节省金额');
      return;
    }

    Loading.show();
    try {
      var resp = await BookkeepingRepo.addBill(
          bookkeepingNumber: state.bookkeepingInfo!.bookkeepingNumber!,
          categoryId: state.selectedTab == 0 ? state.selectedOutcomeCategory!.bookkeepingCategoryId! : state.selectedIncomeCategory!.bookkeepingCategoryId!,
          action: state.selectedTab == 0 ? '2' : '1',
          money: state.bookkeepingResult!.toStringAsFixed(2),
          isSave: (state.savingResult != null && state.savingResult! > 0) ? '2' : '1',
          isSaveMoney: (state.savingResult != null && state.savingResult! > 0) ? state.savingResult?.toStringAsFixed(2) : null,
          isNecessaryStatus: state.noNeed ? '2' : '1',
          nowTime: dateFormat.format(state.selectedDay),
          accountId: selectedAccount!.id!,
          memo: state.remarkStr,
          moneyLogId: logId);

      if (resp.code == 1) {
        showToast('添加流水成功');
        if (needPop == true) {
          RouterHelper.router.pop();
        } else {
          // 重置状态
          textController.text = '';
          state = state.copyWith(
            isSavingInput: false,
            clearBookkeepingResult: true,
            clearSavingResult: true,
            noNeed: false,
            remarkStr: '',
            textInput: '',
          );
        }
      } else {
        showToast(resp.msg ?? '添加流水失败');
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('addBill error $e');
      showToast('添加流水失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  // 切换选项卡
  void selectTab(int tab) {
    if (tab == 1) {
      // 切换到收入时，重置节省相关状态
      state = state.copyWith(
        selectedTab: tab,
        isSavingInput: false,
        clearSavingResult: true,
      );
      textController.text = state.bookkeepingResult?.toStringAsFixed(2) ?? '';
    } else {
      state = state.copyWith(selectedTab: tab);
    }
  }

  // 选择账本
  void selectBookkeeping(BookkeepingInfo? bookkeeping) {
    state = state.copyWith(bookkeepingInfo: bookkeeping);
  }

  // 选择分类
  void selectCategory(CategoryItem? category) {
    if (state.selectedTab == 0) {
      state = state.copyWith(selectedOutcomeCategory: category);
    } else {
      state = state.copyWith(selectedIncomeCategory: category);
    }
  }

  // 选择账户
  void selectAccount(AccountModel? account) {
    if (state.selectedTab == 0) {
      state = state.copyWith(selectedOutcomeAccount: account);
    } else {
      state = state.copyWith(selectedIncomeAccount: account);
    }
  }

  // 设置备注
  void setRemark(String remark) {
    state = state.copyWith(remarkStr: remark);
  }

  // 切换必要性
  void toggleNecessary() {
    state = state.copyWith(noNeed: !state.noNeed);
  }

  // 处理节省金额输入
  void handleSavingTap() {
    if (state.isSavingInput) {
      if (textController.text.isNotEmpty) {
        var [isSuccess, result] = _calculate(textController.text);
        if (isSuccess) {
          state = state.copyWith(
            isSavingInput: false,
            savingResult: result,
          );
          textController.text = state.bookkeepingResult!.toStringAsFixed(2);
        }
      } else {
        state = state.copyWith(
          isSavingInput: false,
          clearSavingResult: true,
        );
        textController.text = state.bookkeepingResult!.toStringAsFixed(2);
      }
    } else {
      if (textController.text.isNotEmpty) {
        var [isSuccess, result] = _calculate(textController.text);
        if (isSuccess) {
          state = state.copyWith(
            isSavingInput: true,
            bookkeepingResult: result,
          );
          textController.text = state.savingResult?.toStringAsFixed(2) ?? '';
        }
      } else {
        showToast('请先输入${state.selectedTab == 0 ? '支出' : '收入'}金额，再输入节省金额');
      }
    }
  }

  // 更新横滑组件顺序
  void updateHorizontalItems(List<ShortcutItem> items) {
    state = state.copyWith(horizontalItems: items);
  }

  // 设置日期
  void setSelectedDay(DateTime day) {
    state = state.copyWith(selectedDay: day);
  }
}
