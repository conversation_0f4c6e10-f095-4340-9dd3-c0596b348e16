import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_category_view.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_keyboard_view.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_notifier.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_provider.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_state.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store_provider.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/widgets/price_view.dart';

class BookkeepingPage extends ConsumerStatefulWidget {
  final dynamic logId;
  final String? targetAccountId;

  const BookkeepingPage({this.logId, this.targetAccountId, super.key});

  @override
  ConsumerState<BookkeepingPage> createState() => _BookkeepingPageState();
}

class _BookkeepingPageState extends ConsumerState<BookkeepingPage> {
  @override
  void initState() {
    super.initState();
    // 使用Riverpod的方式初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(bookkeepingProvider.notifier).initializeData(
            logId: widget.logId,
            targetAccountId: widget.targetAccountId,
          );
    });
  }

  void _showSettingsMenu() {
    final state = ref.read(bookkeepingProvider);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _SettingsMenuPanel(
        items: state.horizontalItems,
        onReorder: (newOrder) {
          ref.read(bookkeepingProvider.notifier).updateHorizontalItems(newOrder);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(bookkeepingProvider);
    final notifier = ref.read(bookkeepingProvider.notifier);

    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBody: true,
      body: SafeArea(
        top: false,
        bottom: false,
        maintainBottomViewPadding: false,
        child: Container(
          child: Stack(
            children: [
              Container(
                color: Colors.transparent,
              ),
              Positioned(
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  child: Image.asset(
                    'assets/images/add_bill_bg.png',
                    fit: BoxFit.fitWidth,
                    width: MediaQuery.of(context).size.width,
                  ),
                  top: 0),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                    leading: IconButton(
                      icon: Image.asset(
                        'assets/images/ic_back.png',
                        width: 24,
                        height: 24,
                      ),
                      onPressed: () {
                        RouterHelper.router.pop();
                      },
                    ),
                    backgroundColor: Colors.transparent,
                    scrolledUnderElevation: 0,
                    title: Image.asset(
                      'assets/images/icon_title.png',
                      width: 129,
                      height: 30,
                    ),
                    actions: [
                      GestureDetector(
                        onTap: () {
                          RouterHelper.router.pushNamed(Routes.chooseLedgarPath, extra: {
                            'selected': state.bookkeepingInfo?.bookkeepingNumber?.isNotEmpty == true ? [state.bookkeepingInfo!.bookkeepingNumber!] : <String>[],
                            'multiSelect': false
                          }).then((value) {
                            if (value is Map && value.containsKey('selected') && value['selected'] is List && value['selected'].isNotEmpty == true) {
                              final bookkeepingList = ref.read(bookkeepingListProvider);
                              final bookkeepingInfo = bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == value['selected'][0]);
                              notifier.selectBookkeeping(bookkeepingInfo);
                            }
                          });
                        },
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/images/hand.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(
                              width: 6,
                            ),
                            if (state.bookkeepingInfo != null)
                              Text(
                                '${state.bookkeepingInfo!.accountBookName}',
                                style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 16, fontWeight: FontWeight.w500),
                              ),
                            const SizedBox(
                              width: 14,
                            )
                          ],
                        ),
                      )
                    ],
                  )),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  child: Builder(builder: (context) {
                    return _calculationView(state, notifier);
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget _calculationView(BookkeepingState state, BookkeepingNotifier notifier) {
    return Column(
      children: [
        const Spacer(),
        Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(20)), color: MColor.xFFFFFFFF),
          child: Column(
            children: [
              const SizedBox(height: 14),
              Row(
                children: [
                  Expanded(
                      child: GestureDetector(
                    onTap: () => notifier.selectTab(0),
                    child: Text(
                      '支出',
                      style: TextStyle(fontSize: 16, height: 1.4, color: state.selectedTab == 0 ? MColor.skin : MColor.xFF999999),
                      textAlign: TextAlign.center,
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    onTap: () => notifier.selectTab(1),
                    child: Text(
                      '收入',
                      style: TextStyle(fontSize: 16, height: 1.4, color: state.selectedTab == 1 ? MColor.skin : MColor.xFF999999),
                      textAlign: TextAlign.center,
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    onTap: () {
                      RouterHelper.router.pushNamed(Routes.debtPath);
                    },
                    child: Text(
                      '借支',
                      style: TextStyle(fontSize: 16, height: 1.4, color: state.selectedTab == 2 ? MColor.skin : MColor.xFF999999),
                      textAlign: TextAlign.center,
                    ),
                  )),
                ],
              ),
              Divider(
                height: 28,
                thickness: 0.5,
                color: MColor.skin.withOpacity(0.2),
                indent: 14,
              ),
              _tabContentView(state, notifier)
            ],
          ),
        ),
        Container(
            padding: EdgeInsets.all(14),
            decoration: BoxDecoration(color: MColor.xFFD7F5E6),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Container(
                          decoration: BoxDecoration(
                            color: Colors.yellow,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          height: 44,
                          child: _buildTextField(state, notifier)),
                    )
                  ],
                ),
                const SizedBox(height: 14),
                BookkeepingKeyboardView(
                  onAppendText: notifier.appendText,
                  onDeleteText: notifier.deleteText,
                  onConfirm: notifier.confirm,
                  bookkeepingName: state.bookkeepingInfo?.accountBookName,
                  onBookkeepingTap: () {
                    RouterHelper.router.pushNamed(Routes.chooseLedgarPath, extra: {
                      'selected': state.bookkeepingInfo?.bookkeepingNumber?.isNotEmpty == true ? [state.bookkeepingInfo!.bookkeepingNumber!] : <String>[],
                      'multiSelect': false
                    }).then((value) {
                      if (value is Map && value.containsKey('selected') && value['selected'] is List && value['selected'].isNotEmpty == true) {
                        final bookkeepingList = ref.read(bookkeepingListProvider);
                        final bookkeepingInfo = bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == value['selected'][0]);
                        notifier.selectBookkeeping(bookkeepingInfo);
                      }
                    });
                  },
                  onSettingsTap: _showSettingsMenu,
                ),
              ],
            )),
      ],
    );
  }

  Widget _buildTextField(BookkeepingState state, BookkeepingNotifier notifier) {
    var prefix = '';
    var label = '';
    var labelColor;
    if (state.isSavingInput) {
      label = state.selectedTab == 0 ? '支出' : '收入';
      prefix = '节省';
      labelColor = MColor.skin;
    } else {
      label = state.savingResult != null ? '节省' : '';
      prefix = state.selectedTab == 0 ? '支出' : '收入';
      labelColor = MColor.xFFFFBE4A;
    }

    return TextField(
      controller: notifier.textController,
      focusNode: notifier.textNode,
      keyboardType: TextInputType.none,
      style: TextStyle(fontSize: 20, color: MColor.xFF1B1C1A, height: 1),
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.always,
        labelText: label,
        labelStyle: TextStyle(fontSize: 14, color: labelColor, height: 1),
        hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
        filled: true,
        fillColor: MColor.xFFFFFFFF,
        prefixIcon: Container(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 10),
              if (state.isSavingInput && state.bookkeepingResult != null) ...[
                PriceView(
                  price: PriceInfo.parsePrice(state.bookkeepingResult!.toStringAsFixed(2)),
                  integerFontSize: 16,
                  fractionalFontSize: 14,
                  textColor: MColor.xFFFFBE4A,
                  prefix: prefix,
                  prefixStyle: TextStyle(fontSize: 16, color: MColor.xFFFFBE4A, height: 1.4),
                  showSymbol: false,
                ),
              ] else if (!state.isSavingInput && state.savingResult != null) ...[
                PriceView(
                  price: PriceInfo.parsePrice(state.savingResult!.toStringAsFixed(2)),
                  integerFontSize: 16,
                  fractionalFontSize: 14,
                  textColor: MColor.skin,
                  prefix: prefix,
                  prefixStyle: TextStyle(fontSize: 16, color: MColor.skin, height: 1.4),
                  showSymbol: false,
                ),
              ] else ...[
                Text(
                  prefix,
                  style: TextStyle(color: state.isSavingInput ? MColor.xFFFFBE4A : MColor.skin, fontSize: 16, height: 1.4),
                ),
              ]
            ],
          ),
        ),
        suffixIcon: Container(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                          context: context,
                          builder: (context) => _RemarkPanel(
                                oldRemark: state.remarkStr,
                              ),
                          useRootNavigator: true)
                      .then((value) {
                    if (value != null) {
                      notifier.setRemark(value as String);
                    }
                  });
                },
                child: RichText(
                  textAlign: TextAlign.center,
                  text: WidgetSpan(
                      child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '备注',
                        style: TextStyle(color: state.remarkStr.isNotEmpty ? MColor.skin : MColor.xFF999999, fontSize: 12, height: 1.4),
                      ),
                      const SizedBox(width: 2),
                      Icon(
                        Icons.edit_document,
                        size: 14,
                        color: state.remarkStr.isNotEmpty ? MColor.skin : MColor.xFF999999,
                      )
                    ],
                  )),
                ),
              ),
            ],
          ),
        ),
        isDense: true,
      ),
      onChanged: (value) {
        // 文本变化已经在notifier中处理
      },
    );
  }

  Widget _tabContentView(BookkeepingState state, BookkeepingNotifier notifier) {
    final outcomeCategories = ref.watch(outcomeCategoriesProvider);
    final incomeCategories = ref.watch(incomeCategoriesProvider);

    switch (state.selectedTab) {
      case 0:
        return BookkeepingCategoryView(
          categoryTabId: 0,
          categories: outcomeCategories,
          initialSelected: state.selectedOutcomeCategory,
          onTap: (categoryItem) {
            notifier.selectCategory(categoryItem);
          },
          onSettingTapped: () {
            RouterHelper.router.pushNamed(Routes.settingCategoryPath, extra: {'tab_id': 0}).then((_) {
              notifier.loadData();
            });
          },
        );
      case 1:
        return BookkeepingCategoryView(
          categoryTabId: 1,
          categories: incomeCategories,
          initialSelected: state.selectedIncomeCategory,
          onTap: (categoryItem) {
            notifier.selectCategory(categoryItem);
          },
          onSettingTapped: () {
            RouterHelper.router.pushNamed(Routes.settingCategoryPath, extra: {'tab_id': 1}).then((_) {
              notifier.loadData();
            });
          },
        );
      default:
        return BookkeepingCategoryView(
          categoryTabId: 2,
          categories: [],
          onTap: (categoryItem) {},
        );
    }
  }
}

// 备注面板
class _RemarkPanel extends StatefulWidget {
  final String oldRemark;

  const _RemarkPanel({required this.oldRemark});

  @override
  State<StatefulWidget> createState() => _RemarkState();
}

class _RemarkState extends State<_RemarkPanel> {
  final TextEditingController _remarkController = TextEditingController();
  final FocusNode _remarkNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _remarkController.text = widget.oldRemark;
  }

  @override
  void dispose() {
    _remarkController.dispose();
    _remarkNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
        data: Theme.of(context).copyWith(
          dividerTheme: DividerThemeData(
            color: Colors.transparent,
          ),
        ),
        child: ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(28)),
            child: Scaffold(
                backgroundColor: MColor.xFFF5F5F5,
                appBar: AppBar(
                  backgroundColor: MColor.xFFF5F5F5,
                  title: Text('填写备注', style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
                  scrolledUnderElevation: 0,
                  centerTitle: true,
                  automaticallyImplyLeading: false,
                  actions: [
                    IconButton(
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                        icon: Icon(Icons.close, size: 22, color: MColor.xFF999999))
                  ],
                ),
                persistentFooterButtons: [
                  GestureDetector(
                    onTap: () {
                      RouterHelper.router.pop(_remarkController.text);
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25),
                          gradient: LinearGradient(
                              begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '确认',
                            style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
                body: SafeArea(
                    child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14.0),
                  child: TextField(
                    autofocus: true,
                    controller: _remarkController,
                    focusNode: _remarkNode,
                    textInputAction: TextInputAction.done,
                    onSubmitted: (value) {
                      RouterHelper.router.pop(_remarkController.text);
                    },
                    style: TextStyle(fontSize: 14, color: MColor.xFF1B1C1A, height: 1),
                    maxLength: 100,
                    decoration: InputDecoration(
                      hintText: '请填写备注',
                      hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
                      filled: true,
                      fillColor: MColor.xFFFFFFFF,
                      isDense: true,
                    ),
                    onChanged: (value) {
                      // 备注变化处理
                    },
                  ),
                )))));
  }
}

// 设置菜单面板
class _SettingsMenuPanel extends StatefulWidget {
  final List<ShortcutItem> items;
  final Function(List<ShortcutItem>) onReorder;

  const _SettingsMenuPanel({
    required this.items,
    required this.onReorder,
  });

  @override
  State<_SettingsMenuPanel> createState() => _SettingsMenuPanelState();
}

class _SettingsMenuPanelState extends State<_SettingsMenuPanel> {
  late List<ShortcutItem> _items;

  @override
  void initState() {
    super.initState();
    _items = List.from(widget.items);
  }

  String _getItemName(ShortcutItem item) {
    switch (item.type) {
      case 'date':
        return '日期选择';
      case 'unnecessary':
        return '非必要';
      case 'saving':
        return '节省';
      case 'accounts':
        return '账户';
      default:
        return item.type;
    }
  }

  IconData _getItemIcon(ShortcutItem item) {
    switch (item.type) {
      case 'date':
        return Icons.calendar_today;
      case 'unnecessary':
        return Icons.remove_circle_outline;
      case 'saving':
        return Icons.savings_outlined;
      case 'accounts':
        return Icons.account_balance_wallet_outlined;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: Color(0xFFF5F5F5))),
            ),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(Icons.close, color: MColor.xFF999999),
                ),
                const Expanded(
                  child: Text(
                    '设置横滑组件',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: MColor.xFF1B1C1A,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    widget.onReorder(_items);
                    Navigator.pop(context);
                  },
                  child: const Text(
                    '完成',
                    style: TextStyle(
                      fontSize: 16,
                      color: MColor.skin,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 提示文字
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              '长按拖动可以调整顺序',
              style: TextStyle(
                fontSize: 14,
                color: MColor.xFF999999,
              ),
            ),
          ),
          // 可拖动列表
          Expanded(
            child: ReorderableListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _items.length,
              onReorder: (oldIndex, newIndex) {
                setState(() {
                  if (newIndex > oldIndex) {
                    newIndex -= 1;
                  }
                  final item = _items.removeAt(oldIndex);
                  _items.insert(newIndex, item);
                });
              },
              itemBuilder: (context, index) {
                final item = _items[index];
                return Container(
                  key: ValueKey(item),
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F8F8),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getItemIcon(item),
                        color: MColor.skin,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _getItemName(item),
                          style: const TextStyle(
                            fontSize: 16,
                            color: MColor.xFF1B1C1A,
                          ),
                        ),
                      ),
                      const Icon(
                        Icons.drag_handle,
                        color: MColor.xFF999999,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
