import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_notifier.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_state.dart';

// BookkeepingNotifier的Provider
final bookkeepingProvider = StateNotifierProvider<BookkeepingNotifier, BookkeepingState>((ref) {
  return BookkeepingNotifier(ref);
});

// 用于获取当前选中的账户的Provider
final selectedAccountProvider = Provider<dynamic>((ref) {
  final state = ref.watch(bookkeepingProvider);
  return state.selectedTab == 0 ? state.selectedOutcomeAccount : state.selectedIncomeAccount;
});

// 用于获取当前选中的分类的Provider
final selectedCategoryProvider = Provider<dynamic>((ref) {
  final state = ref.watch(bookkeepingProvider);
  return state.selectedTab == 0 ? state.selectedOutcomeCategory : state.selectedIncomeCategory;
});

// 用于获取当前输入状态的Provider
final inputStateProvider = Provider<Map<String, dynamic>>((ref) {
  final state = ref.watch(bookkeepingProvider);
  return {
    'prefix': state.isSavingInput ? '节省' : (state.selectedTab == 0 ? '支出' : '收入'),
    'label': state.isSavingInput ? (state.selectedTab == 0 ? '支出' : '收入') : (state.savingResult != null ? '节省' : ''),
    'labelColor': state.isSavingInput ? 'skin' : 'yellow',
  };
});
