import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/extensions.dart';
import 'package:qiazhun/tools/tools.dart';

class BookkeepingShortcutView extends StatelessWidget {
  final DateTime selectedDay;
  final bool noNeed;
  final bool isSavingInput;
  final int selectedTab;
  final AccountModel? selectedIncomeAccount;
  final AccountModel? selectedOutcomeAccount;
  final String? targetAccountId;
  final BookkeepingContextResp? bookkeepingContext;
  final List<AccountModel> shortcutIncomeAccounts;
  final List<AccountModel> shortcutOutcomeAccounts;
  final List<AccountModel> allAccounts;
  final Function(DateTime) onDateSelected;
  final Function() onUnnecessaryToggle;
  final Function() onSavingTap;
  final Function(AccountModel) onAccountSelected;
  final Function() onRefreshData;

  const BookkeepingShortcutView({
    super.key,
    required this.selectedDay,
    required this.noNeed,
    required this.isSavingInput,
    required this.selectedTab,
    this.selectedIncomeAccount,
    this.selectedOutcomeAccount,
    this.targetAccountId,
    this.bookkeepingContext,
    required this.shortcutIncomeAccounts,
    required this.shortcutOutcomeAccounts,
    required this.allAccounts,
    required this.onDateSelected,
    required this.onUnnecessaryToggle,
    required this.onSavingTap,
    required this.onAccountSelected,
    required this.onRefreshData,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> items = [];

    // 添加日期选择器
    items.add(_buildDateItem(context));

    // 添加非必要开关
    items.add(_buildUnnecessaryItem());

    // 添加节省开关（仅支出时显示）
    if (selectedTab == 0) {
      items.add(_buildSavingItem());
    }

    // 添加账户相关项目
    if (targetAccountId != null) {
      // 如果传入了targetAccountId，只显示该账户
      AccountModel? targetAccount;
      try {
        targetAccount = allAccounts.firstWhere(
          (account) => account.id.toString() == targetAccountId,
        );
      } catch (e) {
        targetAccount = null;
      }
      if (targetAccount != null) {
        items.add(_buildAccountItem(targetAccount, isTarget: true));
      }
    } else {
      // 添加最后选择的账户
      final lastAccount = selectedTab == 0 ? bookkeepingContext?.lastOutcomeAccount : bookkeepingContext?.lastIncomeAccount;
      if (lastAccount != null) {
        items.add(_buildAccountItem(lastAccount, isLast: true));
      }

      // 添加快捷账户
      final shortcutAccounts = selectedTab == 0 ? shortcutOutcomeAccounts : shortcutIncomeAccounts;
      for (var account in shortcutAccounts) {
        // 避免重复添加最后选择的账户
        if (lastAccount == null || account.id != lastAccount.id) {
          items.add(_buildAccountItem(account, isShortcut: true));
        }
      }

      // 添加设置按钮
      items.add(_buildSettingsItem(context));
    }

    return SizedBox(
      height: 28,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: items.length,
        separatorBuilder: (context, index) => const SizedBox(width: 10),
        itemBuilder: (context, index) => items[index],
      ),
    );
  }

  Widget _buildDateItem(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDatePicker(
          locale: const Locale('zh', 'CN'),
          context: context,
          firstDate: DateTime(2020, 1, 1),
          currentDate: selectedDay,
          lastDate: DateTime.now().nextYear(),
        ).then((date) {
          if (date != null) {
            onDateSelected(date);
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        decoration: BoxDecoration(
          color: MColor.xFFFFF4E1,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: MColor.xFFFFBE4A, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset('assets/images/ic_cal_calendar.png', width: 16, height: 16),
            const SizedBox(width: 4),
            Text(
              selectedDay.isSameDay(DateTime.now()) ? '今天' : DateFormat('M月d日').format(selectedDay),
              style: const TextStyle(color: MColor.xFFFFBE4A, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnnecessaryItem() {
    return GestureDetector(
      onTap: onUnnecessaryToggle,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        decoration: BoxDecoration(
          color: noNeed ? MColor.xFFFFFFFF : MColor.xFFEEEEEE,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: noNeed ? MColor.skin : MColor.xFF999999, width: 1),
        ),
        child: Row(
          children: [
            Text(
              '非必要',
              style: TextStyle(
                color: noNeed ? MColor.skin : MColor.xFF999999,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSavingItem() {
    return GestureDetector(
      onTap: onSavingTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        decoration: BoxDecoration(
          color: MColor.xFFFFF4E1,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: MColor.xFFFFBE4A, width: 1),
        ),
        child: Row(
          children: [
            const Text(
              '节省',
              style: TextStyle(color: MColor.xFFFFBE4A, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountItem(AccountModel account, {bool isLast = false, bool isShortcut = false, bool isTarget = false}) {
    final selectedAccount = selectedTab == 0 ? selectedOutcomeAccount : selectedIncomeAccount;
    final isSelected = selectedAccount?.id == account.id;

    Color borderColor;
    Color textColor;
    Color bgColor;

    if (isSelected) {
      borderColor = MColor.skin;
      textColor = MColor.xFFFFFFFF;
      bgColor = MColor.skin;
    } else if (isTarget) {
      borderColor = MColor.xFFCB322E;
      textColor = MColor.xFFCB322E;
      bgColor = MColor.xFFFFFFFF;
    } else if (isLast) {
      borderColor = MColor.xFFFFBE4A;
      textColor = MColor.xFFFFBE4A;
      bgColor = MColor.xFFFFFFFF;
    } else {
      borderColor = MColor.xFFEEEEEE;
      textColor = MColor.xFF999999;
      bgColor = MColor.xFFFFFFFF;
    }

    return GestureDetector(
      onTap: isTarget ? null : () => onAccountSelected(account),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: borderColor, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset('assets/images/ic_card.png', width: 16, height: 16),
            const SizedBox(width: 4),
            Text(
              account.accountName ?? '账户',
              style: TextStyle(color: textColor, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsItem(BuildContext context) {
    return GestureDetector(
      onTap: () => _showAccountSelectionDialog(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: MColor.skin, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset('assets/images/ic_card.png', width: 16, height: 16),
            const SizedBox(width: 4),
            const Text(
              '账户',
              style: TextStyle(color: MColor.skin, fontSize: 12),
            ),
            const SizedBox(width: 4),
            Image.asset('assets/images/ic_setting.png', width: 12, height: 12),
          ],
        ),
      ),
    );
  }

  void _showAccountSelectionDialog(BuildContext context) {
    RouterHelper.router.pushNamed(
      Routes.customPopupPath,
      extra: {
        'title': '选择账户',
        'widget': _AccountSelectionWidget(
          selectedTab: selectedTab,
          lastAccount: selectedTab == 0 ? bookkeepingContext?.lastOutcomeAccount : bookkeepingContext?.lastIncomeAccount,
          shortcutAccounts: selectedTab == 0 ? shortcutOutcomeAccounts : shortcutIncomeAccounts,
          allAccounts: allAccounts.where((account) {
            // 根据支出/收入类型过滤账户
            if (selectedTab == 0) {
              // 支出：排除收入类型账户
              return account.cardType != '5';
            } else {
              // 收入：排除支出类型账户
              return account.cardType != '4';
            }
          }).toList(),
          onAccountSelected: onAccountSelected,
          onSetShortcut: _setShortcutAccount,
          onCancelShortcut: _cancelShortcutAccount,
          onRefreshData: onRefreshData,
        ),
        'onConfirm': () async {
          // 确认操作
        },
      },
    );
  }

  Future<void> _setShortcutAccount(AccountModel account) async {
    try {
      final response = await BookkeepingRepo.setShortcutAccount(
        type: selectedTab == 0 ? 1 : 2,
        accountId: account.id.toString(),
      );
      if (response.code == 1) {
        showToast('设置快捷账户成功');
        onRefreshData();
      } else {
        showToast(response.msg ?? '设置失败');
      }
    } catch (e) {
      showToast('设置失败: $e');
    }
  }

  Future<void> _cancelShortcutAccount(AccountModel account) async {
    try {
      final response = await BookkeepingRepo.cancelShortcutAccount(
        type: selectedTab == 0 ? 1 : 2,
        accountId: account.id.toString(),
      );
      if (response.code == 1) {
        showToast('取消快捷账户成功');
        onRefreshData();
      } else {
        showToast(response.msg ?? '取消失败');
      }
    } catch (e) {
      showToast('取消失败: $e');
    }
  }
}

class _AccountSelectionWidget extends StatefulWidget {
  final int selectedTab;
  final AccountModel? lastAccount;
  final List<AccountModel> shortcutAccounts;
  final List<AccountModel> allAccounts;
  final Function(AccountModel) onAccountSelected;
  final Function(AccountModel) onSetShortcut;
  final Function(AccountModel) onCancelShortcut;
  final Function() onRefreshData;

  const _AccountSelectionWidget({
    required this.selectedTab,
    this.lastAccount,
    required this.shortcutAccounts,
    required this.allAccounts,
    required this.onAccountSelected,
    required this.onSetShortcut,
    required this.onCancelShortcut,
    required this.onRefreshData,
  });

  @override
  State<_AccountSelectionWidget> createState() => _AccountSelectionWidgetState();
}

class _AccountSelectionWidgetState extends State<_AccountSelectionWidget> {
  bool _isManageMode = false;
  List<AccountModel> _reorderableShortcuts = [];

  @override
  void initState() {
    super.initState();
    _reorderableShortcuts = List.from(widget.shortcutAccounts);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题栏
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.selectedTab == 0 ? '支出账户' : '收入账户',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: MColor.xFF1B1C1A),
            ),
            if (widget.shortcutAccounts.isNotEmpty)
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isManageMode = !_isManageMode;
                  });
                },
                child: Text(
                  _isManageMode ? '完成' : '管理',
                  style: const TextStyle(fontSize: 14, color: MColor.skin),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),

        if (_isManageMode) ...[
          // 管理模式：只显示快捷账户，可拖动排序
          _buildManageMode(),
        ] else ...[
          // 普通模式：显示所有账户
          _buildNormalMode(),
        ],
      ],
    );
  }

  Widget _buildManageMode() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '拖动排序快捷账户',
          style: TextStyle(fontSize: 14, color: MColor.xFF999999),
        ),
        const SizedBox(height: 12),
        ReorderableListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _reorderableShortcuts.length,
          onReorder: (oldIndex, newIndex) {
            setState(() {
              if (newIndex > oldIndex) {
                newIndex -= 1;
              }
              final item = _reorderableShortcuts.removeAt(oldIndex);
              _reorderableShortcuts.insert(newIndex, item);
            });
            _saveShortcutOrder();
          },
          itemBuilder: (context, index) {
            final account = _reorderableShortcuts[index];
            return _buildAccountTile(
              account,
              key: ValueKey(account.id),
              showTrailing: true,
              trailingWidget: const Icon(Icons.drag_handle, color: MColor.xFF999999),
            );
          },
        ),
      ],
    );
  }

  Widget _buildNormalMode() {
    List<Widget> children = [];

    // 最后选择的账户
    if (widget.lastAccount != null) {
      children.add(_buildSectionTitle('最近使用'));
      children.add(_buildAccountTile(widget.lastAccount!, isLast: true));
      children.add(const SizedBox(height: 16));
    }

    // 快捷账户
    if (widget.shortcutAccounts.isNotEmpty) {
      children.add(_buildSectionTitle('快捷账户'));
      for (var account in widget.shortcutAccounts) {
        if (widget.lastAccount == null || account.id != widget.lastAccount!.id) {
          children.add(_buildAccountTile(account, isShortcut: true));
        }
      }
      children.add(const SizedBox(height: 16));
    }

    // 分割线
    children.add(const Divider(color: MColor.xFFEEEEEE, height: 1));
    children.add(const SizedBox(height: 16));

    // 其他账户
    children.add(_buildSectionTitle('其他账户'));
    final otherAccounts = widget.allAccounts.where((account) {
      final isLast = widget.lastAccount?.id == account.id;
      final isShortcut = widget.shortcutAccounts.any((shortcut) => shortcut.id == account.id);
      return !isLast && !isShortcut;
    }).toList();

    for (var account in otherAccounts) {
      children.add(_buildAccountTile(account));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: MColor.xFF999999),
      ),
    );
  }

  Widget _buildAccountTile(
    AccountModel account, {
    Key? key,
    bool isLast = false,
    bool isShortcut = false,
    bool showTrailing = false,
    Widget? trailingWidget,
  }) {
    final isInShortcuts = widget.shortcutAccounts.any((shortcut) => shortcut.id == account.id);

    return Container(
      key: key,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        leading: Image.asset('assets/images/ic_card.png', width: 24, height: 24),
        title: Text(
          account.accountName ?? '账户',
          style: const TextStyle(fontSize: 14, color: MColor.xFF1B1C1A),
        ),
        trailing: showTrailing
            ? trailingWidget
            : isInShortcuts
                ? GestureDetector(
                    onTap: () => widget.onCancelShortcut(account),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: MColor.xFFFF7858,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '取消快捷',
                        style: TextStyle(fontSize: 12, color: MColor.xFFFFFFFF),
                      ),
                    ),
                  )
                : GestureDetector(
                    onTap: () => widget.onSetShortcut(account),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: MColor.skin,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '添加快捷',
                        style: TextStyle(fontSize: 12, color: MColor.xFFFFFFFF),
                      ),
                    ),
                  ),
        onTap: () {
          widget.onAccountSelected(account);
          RouterHelper.router.pop();
        },
      ),
    );
  }

  Future<void> _saveShortcutOrder() async {
    try {
      final accountIds = _reorderableShortcuts.map((account) => account.id.toString()).toList();
      await BookkeepingRepo.sortShortcutAccounts(
        type: widget.selectedTab == 0 ? 1 : 2,
        accounts: accountIds,
      );
      widget.onRefreshData();
    } catch (e) {
      showToast('保存排序失败: $e');
    }
  }
}
