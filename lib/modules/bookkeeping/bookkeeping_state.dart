// 1. 定义你的状态类 (State)
// 最好是不可变 (immutable) 的，这样更容易追踪状态变化
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

@immutable
class BookkeepingState {
  final String itemName;
  final double amount;

  const BookkeepingState({this.itemName = '', this.amount = 0.0});

  // 创建一个 copyWith 方法，方便创建新状态
  BookkeepingState copyWith({String? itemName, double? amount}) {
    return BookkeepingState(
      itemName: itemName ?? this.itemName,
      amount: amount ?? this.amount,
    );
  }
}

// 2. 创建一个 StateNotifier 来管理你的状态
class BookkeepingNotifier extends StateNotifier<BookkeepingState> {
  // 初始化状态
  BookkeepingNotifier() : super(const BookkeepingState());

  // 提供一个方法来更新状态
  void updateBookkeepingData(String newItemName, double newAmount) {
    // 创建一个新的状态实例来替换旧的
    // Riverpod 会检测到状态对象发生了变化，并通知监听者
    state = state.copyWith(itemName: newItemName, amount: newAmount);
  }
}
