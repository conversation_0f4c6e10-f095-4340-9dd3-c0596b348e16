import 'package:flutter/material.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';

class ShortcutItem {
  final String type;
  final String? icon;
  final Function()? onTap;

  ShortcutItem({required this.type, this.icon, this.onTap});

  @override
  bool operator ==(Object other) => identical(this, other) || other is ShortcutItem && runtimeType == other.runtimeType && type == other.type;

  @override
  int get hashCode => type.hashCode;
}

// 记账页面状态类
@immutable
class BookkeepingState {
  // 日期相关
  final DateTime selectedDay;

  // 账户相关
  final AccountModel? selectedIncomeAccount;
  final AccountModel? selectedOutcomeAccount;

  // 账本相关
  final BookkeepingInfo? bookkeepingInfo;

  // 分类相关
  final CategoryItem? selectedOutcomeCategory;
  final CategoryItem? selectedIncomeCategory;

  // 快捷账户相关
  final BookkeepingContextResp? bookkeepingContext;
  final List<AccountModel> shortcutIncomeAccounts;
  final List<AccountModel> shortcutOutcomeAccounts;

  // 选项卡和金额相关
  final int selectedTab;
  final double? bookkeepingResult;
  final double? savingResult;
  final bool noNeed;
  final bool isSavingInput;
  final String remarkStr;
  final String textInput;

  // 横滑组件的项目顺序
  final List<ShortcutItem> horizontalItems;

  // 加载状态
  final bool isLoading;

  BookkeepingState({
    DateTime? selectedDay,
    this.selectedIncomeAccount,
    this.selectedOutcomeAccount,
    this.bookkeepingInfo,
    this.selectedOutcomeCategory,
    this.selectedIncomeCategory,
    this.bookkeepingContext,
    this.shortcutIncomeAccounts = const [],
    this.shortcutOutcomeAccounts = const [],
    this.selectedTab = 0,
    this.bookkeepingResult,
    this.savingResult,
    this.noNeed = false,
    this.isSavingInput = false,
    this.remarkStr = '',
    this.textInput = '',
    this.horizontalItems = const [],
    this.isLoading = false,
  }) : selectedDay = selectedDay ?? DateTime.now();

  // 创建一个 copyWith 方法来创建状态的副本
  BookkeepingState copyWith({
    DateTime? selectedDay,
    AccountModel? selectedIncomeAccount,
    AccountModel? selectedOutcomeAccount,
    BookkeepingInfo? bookkeepingInfo,
    CategoryItem? selectedOutcomeCategory,
    CategoryItem? selectedIncomeCategory,
    BookkeepingContextResp? bookkeepingContext,
    List<AccountModel>? shortcutIncomeAccounts,
    List<AccountModel>? shortcutOutcomeAccounts,
    int? selectedTab,
    double? bookkeepingResult,
    double? savingResult,
    bool? noNeed,
    bool? isSavingInput,
    String? remarkStr,
    String? textInput,
    List<ShortcutItem>? horizontalItems,
    bool? isLoading,
    // 特殊处理nullable字段
    bool clearSelectedIncomeAccount = false,
    bool clearSelectedOutcomeAccount = false,
    bool clearBookkeepingInfo = false,
    bool clearSelectedOutcomeCategory = false,
    bool clearSelectedIncomeCategory = false,
    bool clearBookkeepingContext = false,
    bool clearBookkeepingResult = false,
    bool clearSavingResult = false,
  }) {
    return BookkeepingState(
      selectedDay: selectedDay ?? this.selectedDay,
      selectedIncomeAccount: clearSelectedIncomeAccount ? null : (selectedIncomeAccount ?? this.selectedIncomeAccount),
      selectedOutcomeAccount: clearSelectedOutcomeAccount ? null : (selectedOutcomeAccount ?? this.selectedOutcomeAccount),
      bookkeepingInfo: clearBookkeepingInfo ? null : (bookkeepingInfo ?? this.bookkeepingInfo),
      selectedOutcomeCategory: clearSelectedOutcomeCategory ? null : (selectedOutcomeCategory ?? this.selectedOutcomeCategory),
      selectedIncomeCategory: clearSelectedIncomeCategory ? null : (selectedIncomeCategory ?? this.selectedIncomeCategory),
      bookkeepingContext: clearBookkeepingContext ? null : (bookkeepingContext ?? this.bookkeepingContext),
      shortcutIncomeAccounts: shortcutIncomeAccounts ?? this.shortcutIncomeAccounts,
      shortcutOutcomeAccounts: shortcutOutcomeAccounts ?? this.shortcutOutcomeAccounts,
      selectedTab: selectedTab ?? this.selectedTab,
      bookkeepingResult: clearBookkeepingResult ? null : (bookkeepingResult ?? this.bookkeepingResult),
      savingResult: clearSavingResult ? null : (savingResult ?? this.savingResult),
      noNeed: noNeed ?? this.noNeed,
      isSavingInput: isSavingInput ?? this.isSavingInput,
      remarkStr: remarkStr ?? this.remarkStr,
      textInput: textInput ?? this.textInput,
      horizontalItems: horizontalItems ?? this.horizontalItems,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
