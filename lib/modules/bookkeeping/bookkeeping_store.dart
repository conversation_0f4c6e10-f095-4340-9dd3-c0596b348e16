import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/tools/tools.dart';

class BookkeepingStore extends GetxController {
  static BookkeepingStore get to => Get.find();

  final List<BookkeepingInfo> bookkeepingList = [];
  final List<AccountModel> accountList = [];

  final List<CategoryItem> outcomeCategories = [];
  final List<CategoryItem> incomeCategories = [];
  final List<CategoryItem> allOutcomeCategories = [];
  final List<CategoryItem> allIncomeCategories = [];

  final List<CategoryItem> officialOutcomes = [];
  final List<CategoryItem> officialIncomes = [];

  int accountListPageId = 0;

  Future<void> getBookkeepingList() async {
    logger.i('getBookkeepingList');
    // Loading.show();
    try {
      var resp = await BookkeepingRepo.getBookkeepingList();
      if (resp.code == 1) {
        bookkeepingList.clear();
        bookkeepingList.addAll(resp.data ?? []);

        List<String> debugIdList = List.generate(bookkeepingList.length, (index) {
          return '${bookkeepingList[index].bookkeepingId}';
        });

        debugPrint('_sortBookkeepings after ${debugIdList.join(',')}');
        update();
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      logger.e('getBookkeepingList error $e');
    } finally {
      // Loading.dismiss();
    }
  }

  Future<void> getMyBookkeepingCategory() async {
    logger.i('getMyBookkeepingCategory');
    try {
      var resp = await BookkeepingRepo.getBookkeepingCategory();
      if (resp.code == 1) {
        incomeCategories.clear();
        var tmpIncome = resp.data?.income ?? [];
        incomeCategories.addAll(tmpIncome);
        outcomeCategories.clear();
        var tmpOutcome = resp.data?.expenses ?? [];
        outcomeCategories.addAll(tmpOutcome);
        officialIncomes.clear();
        officialIncomes.addAll(resp.data?.officialIncome ?? []);
        officialOutcomes.clear();
        officialOutcomes.addAll(resp.data?.officialExpenses ?? []);
        update();
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('getMyBookkeepingCategory error $e');
    }
  }

  Future<void> getBookkeepingCategory(String categoryType) async {
    logger.i('getBookkeepingCategory');
    try {
      var resp = await BookkeepingRepo.getBookkeepingCategoryPage(categoryType);
      if (resp.code == 1) {
        if (categoryType == '1') {
          allOutcomeCategories.clear();
          allOutcomeCategories.addAll(resp.data ?? []);
          update();
        } else {
          allIncomeCategories.clear();
          allIncomeCategories.addAll(resp.data ?? []);
          update();
        }
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('getBookkeepingCategory error $e');
    }
  }

  Future<void> getAccountList() async {
    logger.i('getAccountList');
    try {
      var resp = await BookkeepingRepo.getAccountList(page: accountListPageId, pageCount: 10);
      if (resp.code == 1) {
        accountList.clear();
        accountList.addAll(resp.data ?? []);
        update();
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('getAccountList error $e');
    }
  }
}
