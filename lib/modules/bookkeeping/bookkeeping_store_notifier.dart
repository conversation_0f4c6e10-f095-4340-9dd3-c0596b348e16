import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store_state.dart';
import 'package:qiazhun/tools/tools.dart';

class BookkeepingStoreNotifier extends StateNotifier<BookkeepingStoreState> {
  BookkeepingStoreNotifier() : super(const BookkeepingStoreState());

  Future<void> getBookkeepingList() async {
    logger.i('getBookkeepingList');
    state = state.copyWith(isLoading: true);
    
    try {
      var resp = await BookkeepingRepo.getBookkeepingList();
      if (resp.code == 1) {
        List<String> debugIdList = List.generate(resp.data?.length ?? 0, (index) {
          return '${resp.data![index].bookkeepingId}';
        });

        debugPrint('_sortBookkeepings after ${debugIdList.join(',')}');
        
        state = state.copyWith(
          bookkeepingList: resp.data ?? [],
          isLoading: false,
        );
      } else {
        showToast(resp.msg ?? '');
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      logger.e('getBookkeepingList error $e');
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> getMyBookkeepingCategory() async {
    logger.i('getMyBookkeepingCategory');
    state = state.copyWith(isLoading: true);
    
    try {
      var resp = await BookkeepingRepo.getBookkeepingCategory();
      if (resp.code == 1) {
        state = state.copyWith(
          incomeCategories: resp.data?.income ?? [],
          outcomeCategories: resp.data?.expenses ?? [],
          officialIncomes: resp.data?.officialIncome ?? [],
          officialOutcomes: resp.data?.officialExpenses ?? [],
          isLoading: false,
        );
      } else {
        showToast(resp.msg ?? '');
        state = state.copyWith(isLoading: false);
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('getMyBookkeepingCategory error $e');
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> getBookkeepingCategory(String categoryType) async {
    logger.i('getBookkeepingCategory');
    state = state.copyWith(isLoading: true);
    
    try {
      var resp = await BookkeepingRepo.getBookkeepingCategoryPage(categoryType);
      if (resp.code == 1) {
        if (categoryType == '1') {
          state = state.copyWith(
            allOutcomeCategories: resp.data ?? [],
            isLoading: false,
          );
        } else {
          state = state.copyWith(
            allIncomeCategories: resp.data ?? [],
            isLoading: false,
          );
        }
      } else {
        showToast(resp.msg ?? '');
        state = state.copyWith(isLoading: false);
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('getBookkeepingCategory error $e');
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> getAccountList() async {
    logger.i('getAccountList');
    state = state.copyWith(isLoading: true);
    
    try {
      var resp = await BookkeepingRepo.getAccountList(page: state.accountListPageId, pageCount: 10);
      if (resp.code == 1) {
        state = state.copyWith(
          accountList: resp.data ?? [],
          isLoading: false,
        );
      } else {
        showToast(resp.msg ?? '');
        state = state.copyWith(isLoading: false);
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('getAccountList error $e');
      state = state.copyWith(isLoading: false);
    }
  }

  void setAccountListPageId(int pageId) {
    state = state.copyWith(accountListPageId: pageId);
  }
}
