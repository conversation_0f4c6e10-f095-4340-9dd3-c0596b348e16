import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store_notifier.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store_state.dart';

// BookkeepingStore的Provider
final bookkeepingStoreProvider = StateNotifierProvider<BookkeepingStoreNotifier, BookkeepingStoreState>((ref) {
  return BookkeepingStoreNotifier();
});

// 便捷的Provider来获取特定数据
final bookkeepingListProvider = Provider((ref) {
  return ref.watch(bookkeepingStoreProvider).bookkeepingList;
});

final accountListProvider = Provider((ref) {
  return ref.watch(bookkeepingStoreProvider).accountList;
});

final outcomeCategoriesProvider = Provider((ref) {
  return ref.watch(bookkeepingStoreProvider).outcomeCategories;
});

final incomeCategoriesProvider = Provider((ref) {
  return ref.watch(bookkeepingStoreProvider).incomeCategories;
});

final allOutcomeCategoriesProvider = Provider((ref) {
  return ref.watch(bookkeepingStoreProvider).allOutcomeCategories;
});

final allIncomeCategoriesProvider = Provider((ref) {
  return ref.watch(bookkeepingStoreProvider).allIncomeCategories;
});

final officialOutcomesProvider = Provider((ref) {
  return ref.watch(bookkeepingStoreProvider).officialOutcomes;
});

final officialIncomesProvider = Provider((ref) {
  return ref.watch(bookkeepingStoreProvider).officialIncomes;
});

final bookkeepingStoreLoadingProvider = Provider((ref) {
  return ref.watch(bookkeepingStoreProvider).isLoading;
});
