import 'package:flutter/foundation.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';

@immutable
class BookkeepingStoreState {
  final List<BookkeepingInfo> bookkeepingList;
  final List<AccountModel> accountList;
  final List<CategoryItem> outcomeCategories;
  final List<CategoryItem> incomeCategories;
  final List<CategoryItem> allOutcomeCategories;
  final List<CategoryItem> allIncomeCategories;
  final List<CategoryItem> officialOutcomes;
  final List<CategoryItem> officialIncomes;
  final int accountListPageId;
  final bool isLoading;

  const BookkeepingStoreState({
    this.bookkeepingList = const [],
    this.accountList = const [],
    this.outcomeCategories = const [],
    this.incomeCategories = const [],
    this.allOutcomeCategories = const [],
    this.allIncomeCategories = const [],
    this.officialOutcomes = const [],
    this.officialIncomes = const [],
    this.accountListPageId = 0,
    this.isLoading = false,
  });

  BookkeepingStoreState copyWith({
    List<BookkeepingInfo>? bookkeepingList,
    List<AccountModel>? accountList,
    List<CategoryItem>? outcomeCategories,
    List<CategoryItem>? incomeCategories,
    List<CategoryItem>? allOutcomeCategories,
    List<CategoryItem>? allIncomeCategories,
    List<CategoryItem>? officialOutcomes,
    List<CategoryItem>? officialIncomes,
    int? accountListPageId,
    bool? isLoading,
  }) {
    return BookkeepingStoreState(
      bookkeepingList: bookkeepingList ?? this.bookkeepingList,
      accountList: accountList ?? this.accountList,
      outcomeCategories: outcomeCategories ?? this.outcomeCategories,
      incomeCategories: incomeCategories ?? this.incomeCategories,
      allOutcomeCategories: allOutcomeCategories ?? this.allOutcomeCategories,
      allIncomeCategories: allIncomeCategories ?? this.allIncomeCategories,
      officialOutcomes: officialOutcomes ?? this.officialOutcomes,
      officialIncomes: officialIncomes ?? this.officialIncomes,
      accountListPageId: accountListPageId ?? this.accountListPageId,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
