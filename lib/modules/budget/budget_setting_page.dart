import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/modules/budget/budget_model.dart';
import 'package:qiazhun/modules/budget/budget_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class BudgetSettingPage extends StatefulWidget {
  final String? expandType;
  const BudgetSettingPage(this.expandType, {super.key});

  @override
  State<StatefulWidget> createState() => _BudgetSettingState();
}

class _BudgetSettingState extends State<BudgetSettingPage> {
  final ScrollController _scrollController = ScrollController();

  BudgetInfo? _weeklyBudget;
  BudgetInfo? _monthlyBudget;
  BudgetInfo? _yearlyBudget;

  final List<AccountList> _accountList = [];

  String? _initialExpandType;

  @override
  void initState() {
    _initialExpandType = widget.expandType;
    _getData();
    BookkeepingStore.to.getBookkeepingList();
    super.initState();
  }

  Future<void> _getData() async {
    Loading.show();
    try {
      var resp = await BudgetRepo.budgetIndex();
      if (resp.code == 1) {
        _weeklyBudget = resp.data?.budgetList?.firstWhereOrNull((el) => el.budgetType == '1');
        _monthlyBudget = resp.data?.budgetList?.firstWhereOrNull((el) => el.budgetType == '2');
        _yearlyBudget = resp.data?.budgetList?.firstWhereOrNull((el) => el.budgetType == '3');
        _accountList.clear();
        if (resp.data?.accountList?.isNotEmpty == true) {
          _accountList.addAll(resp.data!.accountList!);
        }
        setState(() {});
      } else {
        showToast(resp.msg ?? '获取账本预算失败');
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.i('BudgetSetting _getData $e');
      showToast('获取账本预算失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _budgetSetting(String budgetType, String budgetAmount, String accountBookNumbers) async {
    Loading.show();
    try {
      var resp = await BudgetRepo.budgetSetting(budgetType, budgetAmount, '1');
      if (resp.code == 1) {
        await _getData();
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.i('BudgetSetting _getData $e');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _budgetSetAmount(String budgetType, String budgetAmount) async {
    Loading.show();
    try {
      var resp = await BudgetRepo.budgetSetAmount(budgetType, budgetAmount);
      if (resp.code == 1) {
        await _getData();
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.i('BudgetSetting _budgetSetAmount $e');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _budgetSetAccountBooks(String budgetType, String accountBookNumbers) async {
    Loading.show();
    try {
      var resp = await BudgetRepo.budgetSetAccountBooks(budgetType, accountBookNumbers);
      if (resp.code == 1) {
        await _getData();
      } else {
        showToast(resp.msg ?? '修改失败');
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 5);
      logger.i('BudgetSetting _budgetSetAccountBooks $e');
      showToast('修改失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _changeDefaultAssets(dynamic cardType) async {
    Loading.show();
    try {
      var resp = await BudgetRepo.changeAccountModule(cardType);
      if (resp.code == 1) {
        await _getData();
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.i('BudgetSetting _changeDefaultAssets $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      extendBody: true,
      body: SafeArea(
        top: false,
        bottom: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '预算设置',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    var sections = [_weeklyBudgetSection, _monthlyBudgetSection, _yearlyBudgetSection, _assetModuleSection];
                    return ListView.separated(
                        controller: _scrollController,
                        padding: EdgeInsets.symmetric(vertical: 14),
                        itemBuilder: (context, index) {
                          return sections[index];
                        },
                        separatorBuilder: (context, index) {
                          return const SizedBox(
                            height: 14,
                          );
                        },
                        itemCount: sections.length);
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget get _weeklyBudgetSection {
    return _BudgetTile(
        scrollController: _scrollController,
        title: '周预算',
        budgetType: '1',
        autoExpand: _initialExpandType == '1',
        budgetInfo: _weeklyBudget,
        onBudgetSet: (budgetAmount) async {
          await _budgetSetAmount('1', budgetAmount);
        },
        onAccountBookChanged: (accountBookNumbers) async {
          await _budgetSetAccountBooks('1', accountBookNumbers);
        },
        onExpanded: (isExpanded) {
          // if (isExpanded) {
          //   _initialExpandType = '1';
          // } else {
          //   _initialExpandType = null;
          // }
          // setState(() {});
        });
  }

  Widget get _monthlyBudgetSection {
    return _BudgetTile(
      scrollController: _scrollController,
      title: '月预算',
      budgetType: '2',
      autoExpand: _initialExpandType == '2',
      budgetInfo: _monthlyBudget,
      onBudgetSet: (budgetAmount) async {
        await _budgetSetAmount('2', budgetAmount);
      },
      onAccountBookChanged: (accountBookNumbers) async {
        await _budgetSetAccountBooks('2', accountBookNumbers);
      },
      onExpanded: (isExpanded) {
        // if (isExpanded) {
        //   _initialExpandType = '2';
        // } else {
        //   _initialExpandType = null;
        // }
        // setState(() {});
      },
    );
  }

  Widget get _yearlyBudgetSection {
    return _BudgetTile(
        scrollController: _scrollController,
        title: '年预算',
        budgetType: '3',
        autoExpand: _initialExpandType == '3',
        budgetInfo: _yearlyBudget,
        onBudgetSet: (budgetAmount) async {
          await _budgetSetAmount('3', budgetAmount);
        },
        onAccountBookChanged: (accountBookNumbers) async {
          await _budgetSetAccountBooks('3', accountBookNumbers);
        },
        onExpanded: (isExpanded) {
          // if (isExpanded) {
          //   _initialExpandType = '3';
          // } else {
          //   _initialExpandType = null;
          // }
          // setState(() {});
        });
  }

  Widget get _assetModuleSection {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(
        color: MColor.xFFFFFFFF,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: MColor.xFF000000.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ListView.separated(
          padding: EdgeInsets.only(top: 14),
          controller: _scrollController,
          shrinkWrap: true,
          itemBuilder: (context, index) {
            if (index == 0) {
              return Column(
                children: [
                  Row(
                    children: [
                      const SizedBox(
                        width: 14,
                      ),
                      Text(
                        '资产显示模块',
                        style: TextStyle(color: MColor.xFF000000, fontSize: 16, height: 1.4),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 14,
                  )
                ],
              );
            }
            return _assetItemView(_accountList[index - 1]);
          },
          separatorBuilder: (context, index) {
            return const SizedBox();
          },
          itemCount: _accountList.length + 1),
    );
  }

  Widget _assetItemView(AccountList account) {
    return Container(
      child: ListTile(
        dense: true,
        title: Text(
          account.accountName ?? '',
          style: TextStyle(color: MColor.xFF000000, fontSize: 15, height: 1.4),
        ),
        subtitle: Text('${account.balance ?? 0.00}元', style: TextStyle(color: MColor.xFF999999, fontSize: 12, height: 1.4)),
        trailing: Icon(
          account.selectedStatus == 1 ? Icons.check_circle : Icons.radio_button_unchecked,
          size: 16,
          color: MColor.skin,
        ),
        onTap: () {
          _changeDefaultAssets(account.cardType);
        },
      ),
    );
  }
}

class _BudgetTile extends StatefulWidget {
  final String title;
  final String budgetType;
  final ScrollController scrollController;
  final bool autoExpand;
  final BudgetInfo? budgetInfo;
  final Function(String budgetAmount)? onBudgetSet;
  final Function(String accountBookIds)? onAccountBookChanged;
  final Function(bool isExpanded)? onExpanded;

  const _BudgetTile(
      {required this.scrollController,
      required this.title,
      required this.autoExpand,
      required this.budgetType,
      required this.budgetInfo,
      required this.onBudgetSet,
      required this.onAccountBookChanged,
      required this.onExpanded});

  @override
  _BudgetTileState createState() => _BudgetTileState();
}

class _BudgetTileState extends State<_BudgetTile> with SingleTickerProviderStateMixin {
  late bool _isExpanded;
  late AnimationController _controller;
  late Animation<double> _heightFactor;

  late List<BookkeepingInfo> _sortedBookkeepings;

  bool _isEditing = false;

  final TextEditingController _textEditingController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.autoExpand;
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _heightFactor = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    if (_isExpanded) {
      _controller.forward();
    }
    _sortBookkeepings();
    _textEditingController.text = widget.budgetInfo?.budgetAmount ?? '0.00';
  }

  void _sortBookkeepings() {
    _sortedBookkeepings = BookkeepingStore.to.bookkeepingList.toList();
    _sortedBookkeepings.sort((BookkeepingInfo bk0, BookkeepingInfo bk1) {
      bool selected0 = widget.budgetInfo?.bookkeepingArrInfo?.any((el) => el.bookkeepingNumber == bk0.bookkeepingNumber) ?? false;
      bool selected1 = widget.budgetInfo?.bookkeepingArrInfo?.any((el) => el.bookkeepingNumber == bk1.bookkeepingNumber) ?? false;
      if (selected0 && selected1) {
        return bk0.bookkeepingNumber?.compareTo(bk1.bookkeepingNumber ?? '') ?? 1;
      } else if (!selected0 && !selected1) {
        return bk0.bookkeepingNumber?.compareTo(bk1.bookkeepingNumber ?? '') ?? 1;
      } else if (selected0) {
        return -1;
      } else {
        return 1;
      }
    });
  }

  @override
  void didUpdateWidget(covariant _BudgetTile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!_isEditing) {
      _textEditingController.text = widget.budgetInfo?.budgetAmount ?? '0.00';
    }

    _sortBookkeepings();

    // _isExpanded = widget.autoExpand;
    // if (_isExpanded) {
    //   _controller.forward();
    // } else {
    //   _controller.reverse();
    // }
  }

  void _handleTap() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse().then<void>((void value) {
          setState(() {
            // Rebuild without widget.children.
          });
        });
      }
      widget.onExpanded?.call(_isExpanded);
    });
  }

  Widget _buildChildren(BuildContext context, Widget? child) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(
        color: MColor.xFFFFFFFF,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: MColor.xFF000000.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Container(
            // decoration: BoxDecoration(
            //   color: MColor.xFFFFFFFF,
            //   borderRadius: BorderRadius.circular(8),
            //   boxShadow: [
            //     BoxShadow(
            //       color: MColor.xFF000000.withOpacity(0.1),
            //       blurRadius: 8,
            //       offset: Offset(0, 4),
            //     ),
            //   ],
            // ),
            child: ListTile(
              title: Text(
                widget.title,
                // style: TextStyle(
                //   color: (double.tryParse(widget.item.profit ?? '') ?? 0.0) >= 0 ? widget.theme.colorScheme.klineUp : widget.theme.colorScheme.klineDown,
                // ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: MColor.xFF999999,
                  ),
                ],
              ),
              onTap: _handleTap,
            ),
          ),
          ClipRect(
            child: Align(
              heightFactor: _heightFactor.value,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 14),
                child: Column(
                  children: [
                    TextField(
                      focusNode: _focusNode,
                      controller: _textEditingController,
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                      textInputAction: TextInputAction.go,
                      decoration: InputDecoration(
                        hintText: '请输入预算金额',
                        hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
                        filled: true,
                        suffixIcon: GestureDetector(
                          onTap: () async {
                            if (_isEditing) {
                              _focusNode.unfocus();
                              String budget = _textEditingController.text;
                              await widget.onBudgetSet?.call(budget.isEmpty ? '0.00' : budget);
                              setState(() {
                                _isEditing = false;
                              });
                            } else {
                              setState(() {
                                _isEditing = true;
                                _focusNode.requestFocus();
                                _textEditingController.text = '';
                              });
                            }
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                _isEditing ? '设置' : '修改',
                                style: TextStyle(fontSize: 14, color: MColor.skin, height: 1.4),
                              ),
                            ],
                          ),
                        ),
                        fillColor: MColor.xFFF5F5F5,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 13),
                        isDense: true,
                      ),
                      readOnly: !_isEditing,
                      onSubmitted: (value) async {
                        _focusNode.unfocus();
                        await widget.onBudgetSet?.call(_textEditingController.text);
                        setState(() {
                          _isEditing = false;
                        });
                      },
                    ),
                    const SizedBox(
                      height: 14,
                    ),
                    Row(
                      children: [
                        Container(
                          height: 13,
                          width: 3,
                          decoration: BoxDecoration(color: MColor.xFFFFD180, borderRadius: BorderRadius.circular(2)),
                        ),
                        const SizedBox(
                          width: 7,
                        ),
                        Text(
                          '账本选择',
                          style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              ..._sortedBookkeepings
                                  .map((account) => GestureDetector(
                                        onTap: () {
                                          List<String> selectedAccounts = [];
                                          widget.budgetInfo?.bookkeepingArrInfo?.forEach((el) {
                                            if (el.bookkeepingNumber?.isNotEmpty == true) {
                                              selectedAccounts.add(el.bookkeepingNumber!);
                                            }
                                          });

                                          bool isSelected =
                                              widget.budgetInfo?.bookkeepingArrInfo?.any((el) => el.bookkeepingNumber == account.bookkeepingNumber) ?? false;

                                          if (isSelected) {
                                            selectedAccounts.removeWhere((number) => number == account.bookkeepingNumber);
                                          } else {
                                            if (account.bookkeepingNumber?.isNotEmpty == true) {
                                              selectedAccounts.add(account.bookkeepingNumber!);
                                            }
                                          }

                                          widget.onAccountBookChanged?.call(selectedAccounts.join(','));
                                        },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                          decoration: BoxDecoration(
                                            color:
                                                widget.budgetInfo?.bookkeepingArrInfo?.any((el) => el.bookkeepingNumber == account.bookkeepingNumber) ?? false
                                                    ? MColor.xFFF5F5F5
                                                    : Colors.white,
                                            borderRadius: BorderRadius.circular(20),
                                            border: Border.all(color: MColor.xFFF0F0F0),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              RoundImage(
                                                imageUrl: account.bookkeepingIcon ?? '',
                                                size: 20,
                                                radius: 10,
                                              ),
                                              const SizedBox(width: 6),
                                              Text(
                                                account.accountBookName ?? '',
                                                style: TextStyle(
                                                  height: 1.4,
                                                  fontSize: 14,
                                                  color: MColor.xFF1B1C1A,
                                                ),
                                              ),
                                              const SizedBox(width: 4),
                                              Icon(
                                                widget.budgetInfo?.bookkeepingArrInfo?.any((el) => el.bookkeepingNumber == account.bookkeepingNumber) ?? false
                                                    ? Icons.close
                                                    : Icons.add,
                                                size: 16,
                                                color: MColor.xFF999999,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ))
                                  .toList(),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 14),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller.view,
      builder: _buildChildren,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
}
