import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/models/user_model.dart';
import 'package:qiazhun/modules/account/account_model.dart';

class UserRepo {
  static Future<BaseModel<UserInfoResp>> getUserInfo() async {
    var resp = await HttpUtil().post('api/user/info');
    return BaseModel.fromJson(resp, (json) => UserInfoResp.fromJson(json));
  }

  static Future<BaseModel<AnnualFeeResp>> getAnnualFeePlan() async {
    var resp = await HttpUtil().post('api/user/annualPlan');
    return BaseModel.fromJson(resp, (json) => AnnualFeeResp.fromJson(json));
  }

  static Future<BaseModel<dynamic>> getConsumptionGuide() async {
    var resp = await HttpUtil().post('api/user/consumerGuide');
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<RepaymentDiaryResp>> getRepaymentDiary() async {
    var resp = await HttpUtil().post('api/user/repaymentLog');
    return BaseModel.fromJson(resp, (json) => RepaymentDiaryResp.fromJson(json));
  }

  static Future<BaseModel<List<dynamic>>> updateUserInfo({String? userName, String? bio, String? avatar}) async {
    var data = {};
    if (userName?.isNotEmpty == true) {
      data['userName'] = userName!;
    }
    if (bio?.isNotEmpty == true) {
      data['bio'] = bio!;
    }
    if (avatar?.isNotEmpty == true) {
      data['avatar'] = avatar!;
    }
    var resp = await HttpUtil().post('api/user/profile', data: data);
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> exemptAnnualFee(int cardId) async {
    var resp = await HttpUtil().post('api/user/setAnnualStatus', data: {'cardId': cardId!});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> membershipOrder(dynamic vipLevelId) async {
    var resp = await HttpUtil().post('api/vip/openVip', data: {'vipConfigId': vipLevelId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> getVipLevel() async {
    var resp = await HttpUtil().post('api/vip/index');
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> deleteAccount() async {
    var resp = await HttpUtil().post('api/user/delete');
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> switchRepayStatus(String accountId, String type, {String? issuedBill}) async {
    var data = {'accountId': accountId, 'type': type};
    if (issuedBill?.isNotEmpty == true) {
      data['issuedBill'] = issuedBill!;
    }
    var resp = await HttpUtil().post('api/user/editChangeStatus', data: data);
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> switchGuideStatus(String accountId) async {
    var resp = await HttpUtil().post('api/user/editChangeUse', data: {'accountId': accountId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<List<dynamic>>> getQuestionList() async {
    var resp = await HttpUtil().post('api/index/questionList');
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>));
  }

  static Future<BaseModel<UserAssetsModel>> getUserAssets() async {
    var resp = await HttpUtil().post(
      'api/user/assets',
    );
    return BaseModel.fromJson(resp, (json) => UserAssetsModel.fromJson(json));
  }

  static Future<BaseModel<UserAssetsModel>> payWithApple(String reciptData) async {
    var resp = await HttpUtil().post('api/apple_pay/verifyReceipt', data: {'sandbox': '0', 'reciptData': reciptData});
    return BaseModel.fromJson(resp, (json) => UserAssetsModel.fromJson(json));
  }

  static Future<BaseModel<dynamic>> unbindApple() async {
    var resp = await HttpUtil().post('api/user/unbindApple');
    return BaseModel.fromJson(resp, (json) => UserAssetsModel.fromJson(json));
  }

  static Future<BaseModel<dynamic>> rebindApple(String identityToken) async {
    var resp = await HttpUtil().post('api/user/bindApple', data: {'identityToken': identityToken});
    return BaseModel.fromJson(resp, (json) => UserAssetsModel.fromJson(json));
  }

  static Future<BaseModel<dynamic>> getWoolHelper() async {
    var resp = await HttpUtil().post('api/user/woolHelper');
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> getDebtCollectionGuide() async {
    var resp = await HttpUtil().post('api/user/collectionGuide');
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> addDebtCollectionRecord({required String cardId, required String price}) async {
    var resp = await HttpUtil().post('api/user/collectionGuideUpdate', data: {'cardId': cardId, 'price': price});
    return BaseModel.fromJson(resp, (json) => json);
  }
}
