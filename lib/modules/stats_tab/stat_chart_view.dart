import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/stats_tab/stat_model.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/legend_widget.dart';

class StatChartView extends StatefulWidget {
  final List<ChartIndexResult> chartIndexResults;
  final DateTime dateRangeStart;
  final DateTime dateRangeEnd;
  final int dateRangeType;

  const StatChartView(this.chartIndexResults, this.dateRangeStart, this.dateRangeEnd, this.dateRangeType, {super.key});
  @override
  State<StatefulWidget> createState() => _StatChartState();
}

class _StatChartState extends State<StatChartView> {
  double _maxData = 0;
  double _minData = 0;
  final _bottomTilesStr = <String>[];
  final List<List<double>> _dataGroups = [];

  @override
  void initState() {
    _handleData();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant StatChartView oldWidget) {
    _handleData();
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.chartIndexResults.isEmpty) {
      return const SizedBox();
    }
    switch (widget.dateRangeType) {
      case 0:
        return _StatDayBarChartView(widget.chartIndexResults[0]);
      case 1:
        return StatBarChartView(_maxData, _minData, _bottomTilesStr, _dataGroups, 12);
      case 2:
        return StatBarChartView(_maxData, _minData, _bottomTilesStr, _dataGroups, 2);
      case 3:
        return StatBarChartView(_maxData, _minData, _bottomTilesStr, _dataGroups, 6);
      case 4:
        return StatLineChartView(_maxData, _minData, _bottomTilesStr, _dataGroups);
      default:
        return const SizedBox();
    }
  }

  void _handleData() {
    if (widget.chartIndexResults.isEmpty) {
      return;
    }
    _bottomTilesStr.clear();
    _dataGroups.clear();

    _maxData = double.tryParse(widget.chartIndexResults[0].incomePrice ?? '') ?? 0.0;
    _minData = double.tryParse(widget.chartIndexResults[0].incomePrice ?? '') ?? 0.0;
    // _maxData = 0;
    // _minData = 100;
    widget.chartIndexResults.asMap().forEach((index, el) {
      double y1 = double.tryParse(el.incomePrice ?? '') ?? 0.0;
      double y2 = double.tryParse(el.expenditurePrice ?? '') ?? 0.0;
      // TODO 测试数据
      // y1 = index + Random().nextInt(20) * 1.0;
      // y2 = index + Random().nextInt(20) * 1.0;
      // TODO end
      _maxData = max(y1, _maxData);
      _maxData = max(y2, _maxData);
      _minData = min(y1, _minData);
      _minData = min(y2, _minData);
      _dataGroups.add([y1, y2]);
    });

    if (widget.dateRangeType == 1 || widget.dateRangeType == 3 || widget.dateRangeType == 2) {
      if (widget.dateRangeType != 0 && widget.dateRangeType != 4) {}
      if (widget.dateRangeType == 1) {
        _bottomTilesStr.addAll(['周一', '周二', '周三', '周四', '周五', '周六', '周日']);
      } else if (widget.dateRangeType == 3) {
        List.generate(12, (i) {
          if (i % 2 == 0) {
            _bottomTilesStr.add('${i + 1}月');
          } else {
            _bottomTilesStr.add('');
          }
        });
      } else if (widget.dateRangeType == 2) {
        List.generate(widget.chartIndexResults.length, (i) {
          if (i % 4 == 0) {
            _bottomTilesStr.add('${i + 1}');
          } else {
            _bottomTilesStr.add('');
          }
        });
      }
    } else if (widget.dateRangeType == 4) {
      var maxBottomTiles = 5;

      DateFormat df = DateFormat('dd');
      List.generate(widget.chartIndexResults.length, (i) {
        if (widget.chartIndexResults.length >= maxBottomTiles) {
          var step = (widget.chartIndexResults.length / (maxBottomTiles - 1)).toInt();
          if (i % step == 0) {
            _bottomTilesStr.add(df.format(widget.dateRangeStart.add(Duration(days: i))));
          } else {
            _bottomTilesStr.add('');
          }
        } else {
          _bottomTilesStr.add(df.format(widget.dateRangeStart.add(Duration(days: i))));
        }
      });
    }
  }
}

class StatBarChartView extends StatefulWidget {
  final double maxData, minData;
  final double barWidth;

  final List<String> bottomTiles;
  final List<List<double>> dataGroups;

  const StatBarChartView(this.maxData, this.minData, this.bottomTiles, this.dataGroups, this.barWidth, {super.key});

  @override
  State<StatefulWidget> createState() => _StatBarChartState();
}

class _StatBarChartState extends State<StatBarChartView> {
  late int _leftAxisInterval;
  late int _leftAxisMaxValue;

  @override
  void initState() {
    _makeBarData();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant StatBarChartView oldWidget) {
    super.didUpdateWidget(oldWidget);
    _makeBarData();
  }

  void _makeBarData() {
    // 获取用户输入的最大值和区间数
    int intervalCount = 6;

    // 计算基数
    double base = widget.maxData / intervalCount;

    // 获取基数的最高位数（整数部分的最大位数）
    String baseString = base.toStringAsFixed(0);
    int highestDigit = int.parse(baseString[0]);

    // 确定最小单位
    int minUnit = pow(10, baseString.length - 1).toInt();

    // 计算区间大小
    _leftAxisInterval = (highestDigit + 1) * minUnit;

    // 生成区间
    for (int i = 0; i <= intervalCount; i++) {
      int value = i * _leftAxisInterval;
      _leftAxisMaxValue = value;
      if (value > widget.maxData) {
        break;
      }
    }
  }

  List<BarChartGroupData> _makeDataGroups() {
    return List.generate(widget.dataGroups.length, (index) {
      return BarChartGroupData(
        barsSpace: 2.4,
        x: index,
        barRods: [
          BarChartRodData(
            toY: widget.dataGroups[index][0],
            color: MColor.skin,
            width: widget.barWidth,
          ),
          BarChartRodData(
            toY: widget.dataGroups[index][1],
            color: MColor.xFFFED58E,
            width: widget.barWidth,
          ),
        ],
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      LegendsListWidget(
        legends: [
          Legend('收入', MColor.skin),
          Legend('支出', MColor.xFFFED58E),
        ],
      ),
      const SizedBox(
        height: 14,
      ),
      AspectRatio(
        aspectRatio: 1.2,
        child: BarChart(
          BarChartData(
            maxY: _leftAxisMaxValue.toDouble(),
            minY: widget.minData,
            gridData: FlGridData(
              show: true,
              drawVerticalLine: false,
              checkToShowHorizontalLine: (value) {
                return true;
              },
              getDrawingHorizontalLine: (value) => FlLine(
                color: MColor.xFFD9D9D9,
                strokeWidth: 1,
              ),
            ),
            titlesData: FlTitlesData(
              show: true,
              rightTitles: const AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              topTitles: AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  getTitlesWidget: bottomTitles,
                  reservedSize: 42,
                ),
              ),
              leftTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 40,
                  interval: _leftAxisInterval.toDouble(),
                  getTitlesWidget: leftTitleWidgets,
                ),
              ),
            ),
            borderData: FlBorderData(
              show: false,
            ),
            barGroups: _makeDataGroups(),
          ),
        ),
      )
    ]);
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(fontWeight: FontWeight.bold, fontSize: 14, color: MColor.xFF8C8C8C);
    String text = '${value.toInt()}';

    // if (widget.leftTiles.contains(text)) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Text(
        text,
        style: style,
        textAlign: TextAlign.center,
        maxLines: 1,
      ),
    );
    // } else {
    //   return Container();
    // }
    // return Container();
  }

  SideTitles leftTitles() => SideTitles(
        getTitlesWidget: leftTitleWidgets,
        showTitles: true,
        interval: _leftAxisInterval.toDouble(),
        reservedSize: 40,
      );

  Widget bottomTitles(double value, TitleMeta meta) {
    int idx = value.toInt();
    if (widget.bottomTiles.length <= idx) {
      return Container();
    }
    final Widget text = Text(
      widget.bottomTiles[idx],
      style: const TextStyle(
        color: MColor.xFF8C8C8C,
        fontSize: 12,
      ),
    );

    return SideTitleWidget(
      axisSide: meta.axisSide,
      space: 16, //margin top
      child: text,
    );
  }
}

class StatLineChartView extends StatefulWidget {
  final double maxData, minData;

  final List<String> bottomTiles;
  final List<List<double>> dataGroups;
  final bool showTitles;
  final bool showSingleLine;
  const StatLineChartView(this.maxData, this.minData, this.bottomTiles, this.dataGroups, {this.showTitles = true, this.showSingleLine = false, super.key});

  @override
  State<StatefulWidget> createState() => _StatLineChartState();
}

class _StatLineChartState extends State<StatLineChartView> {
  late LineChartData _lineChartData;

  late int _leftAxisInterval;
  late int _leftAxisMaxValue;
  @override
  void initState() {
    _makeLineData();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant StatLineChartView oldWidget) {
    _makeLineData();
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        LegendsListWidget(
          legends: widget.showTitles
              ? [
                  Legend('收入', MColor.skin),
                  Legend('支出', MColor.xFFFED58E),
                ]
              : [],
        ),
        const SizedBox(
          height: 14,
        ),
        AspectRatio(
          aspectRatio: 1.2,
          child: LineChart(
            _lineChartData,
          ),
        ),
      ],
    );
  }

  void _makeLineData() {
    // 获取用户输入的最大值和区间数
    int intervalCount = 6;

    // 计算基数
    double base = (widget.maxData - widget.minData) / intervalCount;
    double margin = (widget.maxData - widget.minData) * 0.1;
    double max = widget.maxData + margin;
    double min = widget.minData - margin;
    double gap = max - min;
    if (base == 0) {
      base = widget.maxData.abs();
    }

    // // 获取基数的最高位数（整数部分的最大位数）
    // String baseString = base.toStringAsFixed(0);
    // int highestDigit = int.parse(baseString[0]);

    // // 确定最小单位
    // int minUnit = pow(10, baseString.length - 1).toInt();

    // // 计算区间大小
    _leftAxisInterval = (gap / intervalCount).ceil();
    if (_leftAxisInterval == 0) {
      _leftAxisInterval = 1;
    }

    // // 生成区间
    // for (int i = 0; i <= intervalCount; i++) {
    //   int value = i * _leftAxisInterval;
    //   _leftAxisMaxValue = value;
    //   if (value > widget.maxData) {
    //     break;
    //   }
    // }

    _lineChartData = LineChartData(
      gridData: const FlGridData(show: true, drawHorizontalLine: true, drawVerticalLine: false),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(sideTitles: bottomTitles, drawBelowEverything: false),
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        leftTitles: AxisTitles(
          sideTitles: leftTitles(),
        ),
      ),
      borderData: borderData,
      lineBarsData: lineBarsData1,
      baselineY: widget.minData,
      minX: 0,
      maxX: widget.dataGroups.length.toDouble(),
      maxY: max,
      minY: min,
    );
  }

  List<LineChartBarData> get lineBarsData1 => List.generate(widget.showSingleLine ? 1 : 2, (index) {
        return LineChartBarData(
          isCurved: false,
          preventCurveOverShooting: true,
          color: index == 0 ? MColor.skin : MColor.xFFFED58E,
          barWidth: 2,
          isStrokeCapRound: true,
          // dotData: const FlDotData(show: false),
          belowBarData: BarAreaData(show: false),
          dotData: FlDotData(
            show: true,
            getDotPainter: (spot, percent, barData, idx) {
              return FlDotCirclePainter(
                radius: 2,
                color: index == 0 ? MColor.skin : MColor.xFFFED58E,
                strokeColor: Colors.white,
                strokeWidth: 1,
              );
            },
          ),
          spots: List.generate(widget.dataGroups.length, (idx) {
            return FlSpot(idx.toDouble(), widget.dataGroups[idx][index]);
          }),
        );
      });

  FlBorderData get borderData => FlBorderData(
        show: true,
        border: Border(
          bottom: BorderSide(color: MColor.xFF8C8C8C, width: 4),
          left: const BorderSide(color: Colors.transparent),
          right: const BorderSide(color: Colors.transparent),
          top: const BorderSide(color: Colors.transparent),
        ),
      );
  Widget leftTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(fontWeight: FontWeight.bold, fontSize: 14, color: MColor.xFF8C8C8C);
    String text = '${value.toInt()}';

    // if (widget.leftTiles.contains(text)) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Text(
        text,
        style: style,
        textAlign: TextAlign.center,
        maxLines: 1,
      ),
    );
    // } else {
    //   return Container();
    // }
    // return Container();
  }

  SideTitles leftTitles() => SideTitles(
        getTitlesWidget: leftTitleWidgets,
        showTitles: true,
        interval: _leftAxisInterval.toDouble(),
        reservedSize: 40,
      );
  SideTitles get bottomTitles => SideTitles(
        showTitles: true,
        minIncluded: true,
        maxIncluded: false,
        reservedSize: 32,
        interval: 1,
        getTitlesWidget: bottomTitleWidgets,
      );
  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(fontSize: 12, color: MColor.xFF999999);
    var idx = value.toInt();
    var str = idx >= widget.bottomTiles.length ? '' : widget.bottomTiles[value.toInt()];
    Widget text = Text(str, style: style);
    return SideTitleWidget(
      axisSide: meta.axisSide,
      space: 10,
      child: text,
    );
  }
}

class StatPieChartView extends StatefulWidget {
  final List<double> percentages;
  final List<String> titles;
  final List<Color> colors;
  final double radius;
  final double innerRaidus;
  const StatPieChartView({super.key, required this.titles, required this.percentages, required this.colors, required this.radius, required this.innerRaidus});

  @override
  State<StatefulWidget> createState() => _StatPieChartState();
}

class _StatPieChartState extends State<StatPieChartView> {
  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.3,
      child: Row(
        children: <Widget>[
          Expanded(
            child: AspectRatio(
              aspectRatio: 1,
              child: PieChart(
                PieChartData(
                  borderData: FlBorderData(show: false),
                  sectionsSpace: 1,
                  centerSpaceRadius: widget.innerRaidus,
                  sections: showingSections(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  final fontSize = 16.0;
  List<PieChartSectionData> showingSections() {
    return List.generate(widget.percentages.length, (i) {
      final radius = widget.radius;
      return PieChartSectionData(
        color: widget.colors[i],
        value: widget.percentages[i],
        title: widget.percentages[i] > 3 ? '${widget.titles[i]}' : '',
        radius: radius,
        titleStyle: TextStyle(
          fontSize: fontSize,
          color: MColor.xFFFFFFFF,
        ),
      );
    });
  }
}

class _StatDayBarChartView extends StatelessWidget {
  final ChartIndexResult result;

  const _StatDayBarChartView(this.result);
  @override
  Widget build(BuildContext context) {
    ChartIndexResult data = result;
    double income = double.tryParse(data.incomePrice ?? '') ?? 0.0;
    double expenditure = double.tryParse(data.expenditurePrice ?? '') ?? 0.0;
    // double income = 1000000.00;
    // double expenditure = 30000000.00;
    double total = income + expenditure;
    TextStyle ts = TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.w400, color: MColor.xFF333333);
    double incomeWidth = textSize(income.toStringAsFixed(2), ts).width;
    double expenditureWidth = textSize(expenditure.toStringAsFixed(2), ts).width;
    double width = max(incomeWidth, expenditureWidth);
    double fullBarWidth = (MediaQuery.of(context).size.width - 74 - 8 - 30 - width);
    double incomeBarWidth = total == 0 ? fullBarWidth : income / total * fullBarWidth;
    double expBarWidth = total == 0 ? fullBarWidth : expenditure / total * fullBarWidth;
    return Column(
      children: [
        Row(
          children: [
            Text('支出', style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF999999)),
            const SizedBox(
              width: 8,
            ),
            Container(
              height: 14,
              width: expBarWidth,
              decoration: BoxDecoration(color: MColor.xFFFED58E, borderRadius: BorderRadius.circular(7)),
            ),
            const SizedBox(
              width: 8,
            ),
            const Spacer(),
            Text(
              '${expenditure.toStringAsFixed(2)}',
              style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF333333),
            )
          ],
        ),
        const SizedBox(
          height: 14,
        ),
        Row(
          children: [
            Text('收入', style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF999999)),
            const SizedBox(
              width: 8,
            ),
            Container(
              height: 14,
              width: incomeBarWidth,
              decoration: BoxDecoration(color: MColor.skin, borderRadius: BorderRadius.circular(7)),
            ),
            const SizedBox(
              width: 8,
            ),
            const Spacer(),
            Text(
              '${income.toStringAsFixed(2)}',
              style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF333333),
            )
          ],
        ),
      ],
    );
  }
}
