import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/widgets/price_view.dart';
import 'package:qiazhun/widgets/triangle.dart';

class TransactionItemAction {
  final String label;
  final IconData icon;
  final Color bgColor;
  final Color fgColor;
  final Function()? onPressed;

  const TransactionItemAction({required this.label, required this.icon, required this.bgColor, required this.fgColor, this.onPressed});
}

class TransactionItemView extends StatefulWidget {
  final TransactionItem data;
  final Widget? headerView;
  final List<TransactionItemAction>? actions;
  const TransactionItemView(this.data, {this.headerView, this.actions, super.key});

  @override
  State<StatefulWidget> createState() => _TransactionItemState();
}

class _TransactionItemState extends State<TransactionItemView> {
  @override
  Widget build(BuildContext context) {
    List<Widget> actions = List.generate(widget.actions?.length ?? 0, (index) {
      var action = widget.actions![index];
      return SlidableAction(
        onPressed: (context) async {
          action.onPressed?.call();
        },
        backgroundColor: action.bgColor,
        foregroundColor: action.fgColor,
        icon: action.icon,
        label: action.label,
      );
    });
    return Column(
      children: [
        if (widget.headerView != null) widget.headerView!,
        Slidable(
          key: ValueKey(widget.data.id),
          // The end action pane is the one at the right or the bottom side.
          endActionPane: ActionPane(
            motion: ScrollMotion(),
            children: actions,
          ),
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              RouterHelper.router.pushNamed(Routes.bookkeepingPath, extra: {'logId': '${widget.data.id}'}).then((_) {});
            },
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      height: 19,
                      child: Column(
                        children: [
                          if (widget.data.couponPrice?.isNotEmpty == true) ...{
                            Container(
                              height: 16,
                              padding: EdgeInsets.symmetric(horizontal: 5, vertical: 1),
                              decoration: BoxDecoration(color: MColor.skin, borderRadius: BorderRadius.circular(8)),
                              child: PriceView(
                                price: PriceInfo.parsePrice(widget.data.couponPrice ?? '0.0'),
                                integerFontSize: 10,
                                fractionalFontSize: 8,
                                textColor: MColor.xFFFFFFFF,
                                showSymbol: false,
                                prefix: '省',
                                prefixStyle: TextStyle(fontSize: 10, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFFFFFFFF),
                              ),
                            ),
                            const Triangle(height: 3, width: 8)
                          }
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: 14,
                    )
                  ],
                ),
                Container(
                  padding: EdgeInsets.fromLTRB(0, 0, 0, 18),
                  child: Row(
                    children: [
                      const SizedBox(
                        width: 14,
                      ),
                      ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(20)),
                        child: CachedNetworkImage(
                          imageUrl: getImageUrl(widget.data.icon),
                          width: 39,
                          height: 39,
                          fit: BoxFit.cover,
                          placeholder: (ctx, e) {
                            return Container(
                              decoration: BoxDecoration(
                                color: MColor.xFFECECEC,
                              ),
                            );
                          },
                          errorWidget: (ctx, e, x) {
                            return Container(
                              decoration: BoxDecoration(
                                color: MColor.skin,
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(
                        width: 14,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(widget.data.categoryName ?? '',
                                style: TextStyle(fontSize: 15, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A)),
                            if (widget.data.memo?.isNotEmpty == true) ...{
                              const SizedBox(
                                height: 6,
                              ),
                              Text(
                                widget.data.memo ?? '',
                                style: TextStyle(fontSize: 12, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFF999999),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              )
                            },
                          ],
                        ),
                      ),
                      const SizedBox(
                        width: 14,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Builder(builder: (context) {
                            bool isAdd = widget.data.type == '1'; //收入
                            return PriceView(
                              price: PriceInfo.parsePrice(widget.data.money ?? '0.0'),
                              integerFontSize: 17,
                              fractionalFontSize: 14,
                              textColor: isAdd ? MColor.xFFCB322E : MColor.skin,
                              prefix: isAdd ? '+' : '-',
                              showSymbol: false,
                              prefixStyle: TextStyle(fontSize: 17, height: 1.4, fontWeight: FontWeight.w500, color: isAdd ? MColor.xFFCB322E : MColor.skin),
                            );
                          }),
                          const SizedBox(
                            height: 2,
                          ),
                          Builder(builder: (context) {
                            return Text(
                              widget.data.accountName ?? '',
                              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: MColor.xFF999999, height: 1.4),
                            );
                          })
                        ],
                      ),
                      const SizedBox(
                        width: 14,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
