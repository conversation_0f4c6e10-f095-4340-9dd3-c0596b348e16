import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/price_view.dart';

class DebtCollectionGuidePage extends StatefulWidget {
  const DebtCollectionGuidePage({super.key});

  @override
  State<StatefulWidget> createState() => _DebtCollectionGuideState();
}

class _DebtCollectionGuideState extends State<DebtCollectionGuidePage> {
  bool _isLoading = true;
  List<dynamic> borrowList = [];
  List<dynamic> lendList = [];

  @override
  void initState() {
    super.initState();
    _isLoading = true;
    _loadData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      // Call the debt collection guide API
      final response = await UserRepo.getDebtCollectionGuide();

      if (response.code == 1) {
        setState(() {
          borrowList = response.data?['borrowList'] ?? [];
          lendList = response.data?['lendList'] ?? [];
          _isLoading = false;
        });
      } else {
        showToast(response.msg ?? '获取数据失败');
      }
    } catch (e) {
      showToast('网络错误，请稍后重试');
      logger.e('Debt collection guide error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showCollectionDialog(dynamic data) {
    final TextEditingController amountController = TextEditingController();

    // 创建输入表单的Widget
    Widget collectionForm = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: MColor.xFFFFFFFF,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Text(
                '账户：',
                style: TextStyle(
                  fontSize: 16,
                  color: MColor.xFF999999,
                ),
              ),
              Text(
                '${data?['accountName'] ?? ''}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: MColor.xFF1B1C1A,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: MColor.xFFFFFFFF,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '讨要金额',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: MColor.xFF1B1C1A,
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: amountController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  hintText: '请输入讨要的金额',
                  hintStyle: TextStyle(color: MColor.xFF999999),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: MColor.xFFEEEEEE),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: MColor.xFFEEEEEE),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: MColor.xFFCB322E),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
              ),
            ],
          ),
        ),
      ],
    );

    // 使用CustomPopupPanel
    RouterHelper.router.pushNamed(
      Routes.customPopupPath,
      extra: {
        'title': '添加讨账记录',
        'widget': collectionForm,
        'onConfirm': () async {
          if (amountController.text.isEmpty) {
            showToast('请输入讨要金额');
            return;
          }
          await _addCollectionRecord(data['id'].toString(), amountController.text);
        },
      },
    );
  }

  Future<void> _addCollectionRecord(String cardId, String amount) async {
    try {
      Loading.show('添加中...');

      final response = await UserRepo.addDebtCollectionRecord(
        cardId: cardId,
        price: amount,
      );

      Loading.dismiss();

      if (response.code == 1) {
        showToast('添加成功');
        _loadData(); // Refresh data
      } else {
        showToast(response.msg ?? '添加失败');
      }
    } catch (e) {
      Loading.dismiss();
      showToast('网络错误，请稍后重试');
      logger.e('Add collection record error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
        title: '讨账指南',
        body: Builder(builder: (context) {
          if (_isLoading) {
            return LoadingView();
          }
          if (borrowList.isEmpty == true && lendList.isEmpty == true) {
            return EmptyView();
          }
          return ListView.separated(
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return Container(
                    child: _itemView(index >= borrowList.length ? lendList[index - borrowList.length] : borrowList[index], index, index >= borrowList.length));
              },
              separatorBuilder: (context, index) {
                if (borrowList.isEmpty || lendList.isEmpty) {
                  return const SizedBox(
                    height: 18,
                  );
                } else if (index == borrowList.length - 1) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 18.0),
                    child: Image.asset(
                      'assets/images/bg_separator_repay_diary.png',
                      width: 343,
                      height: 31,
                    ),
                  );
                }
                return const SizedBox(
                  height: 18,
                );
              },
              itemCount: borrowList.length + lendList.length);
        }));
  }

  Widget _itemView(dynamic data, int index, bool isUsed) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 14),
      padding: EdgeInsets.all(18),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                data?['accountName'],
                style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.w600, color: MColor.xFF1B1C1A),
              ),
              const Spacer(),
              const SizedBox(
                height: 4,
              ),
              Text(
                '${data?['residueDay']}',
                style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFCB322E),
              ),
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  PriceView(price: PriceInfo.parsePrice(data?['price'] ?? '0.00'), integerFontSize: 20, fractionalFontSize: 16, textColor: MColor.xFFCB322E),
                  // const SizedBox(
                  //   width: 8,
                  // ),
                  // Builder(builder: (context) {
                  //   DateTime? dt = DateFormat('MM/dd').tryParse(data.repaymentDate ?? '');
                  //   return dt == null
                  //       ? const SizedBox()
                  //       : Text(
                  //           '${dt.month}月${dt.day}日 账单日',
                  //           style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFFFFFFF),
                  //         );
                  // }),
                ],
              ),
              const Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () async {
                  if (!isUsed) {
                    // 如果是"未讨要"状态，弹出对话框
                    _showCollectionDialog(data);
                  } else {
                    // 如果是"已讨要"状态，切换状态
                    Loading.show();
                    try {
                      var resp = await UserRepo.switchGuideStatus('${data.id}');
                      if (resp.code == 1) {
                        _loadData();
                        showToast('操作成功');
                      } else {
                        showToast(resp.msg ?? '操作失败');
                      }
                    } catch (e) {
                      showToast('操作失败 $e');
                    } finally {
                      Loading.dismiss();
                    }
                  }
                },
                child: Row(
                  children: [
                    Image.asset(
                      isUsed ? 'assets/images/ic_debt_used.png' : 'assets/images/ic_debt_unused.png',
                      width: 20,
                      height: 20,
                      fit: BoxFit.scaleDown,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Text(
                      isUsed ? '已讨要' : '未讨要',
                      style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF1B1C1A),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                width: 14,
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  RouterHelper.router.pushNamed(Routes.editAccountPath, extra: {'accountId': data['id']}).then((value) {
                    _loadData();
                  });
                },
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/ic_debt_manage.png',
                      width: 18,
                      height: 18,
                      fit: BoxFit.scaleDown,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Text(
                      '管理',
                      style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF1B1C1A),
                    ),
                  ],
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
