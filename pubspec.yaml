name: qiazhun
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.0+10100

environment:
  sdk: '>=3.4.0 <4.0.0'

fluwx:
  app_id: 'wxfa8f65bc326d6783'
  debug_logging: true # Logging in debug mode.
  android:
#    interrupt_wx_request: true # Defaults to true.
#    flutter_activity: 'MainActivity' # Defaults to app's launcher
  ios:
    universal_link: https://ji.qiazhun.com

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  riverpod: ^2.5.1
  get: 4.6.6
  fluwx: ^4.5.5
  flutter_riverpod: ^2.5.1
  logger: ^2.3.0
  cached_network_image: ^3.4.1
  flutter_easyloading: ^3.0.5
  package_info_plus: ^8.0.0
  fluttertoast: ^8.2.5
  dio: ^5.4.3+1
  json_annotation: ^4.9.0
  after_layout: ^1.2.0
  url_launcher: ^6.3.1
  shared_preferences: ^2.2.3
  math_expressions: ^2.6.0
  intl: ^0.20.2
  flutter_spinkit: ^5.2.1
  go_router: ^14.1.4
  flutter_smart_dialog: ^4.9.7+4
  flutter_screenutil: ^5.9.3
  table_calendar: ^3.1.2
  image_picker: ^1.1.2
  lunar: ^1.7.0
  fl_chart: ^0.69.0
  flutter_slidable: ^3.1.1
  flutter_inappwebview: ^6.1.5
  sign_in_with_apple: ^6.1.4
  in_app_purchase: ^3.2.0
  in_app_purchase_android: ^0.3.0
  in_app_purchase_storekit: ^0.3.4
  retrofit: ^4.4.2
  flutter_localizations:
    sdk: flutter
  flutter:
    sdk: flutter


  # flutter_unionad: ^2.1.8
    # path: ./flutter_unionad

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

dev_dependencies:
  build_runner:
  custom_lint:
  riverpod_generator: ^2.4.0
  riverpod_lint: ^2.3.10
  retrofit_generator: '>=8.0.0 <10.0.0'
  json_serializable: ^6.8.0
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Usama Azad <https://github.com/Usama-Azad>
  # Generate by [flutter_asset_manager](https://github.com/Usama-Azad/flutter_asset_manager) library.
  # PLEASE DO NOT EDIT MANUALLY.
  assets:
    - assets/images/

  # To add assets to your application, add an assets section, like this:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_intl:
  enabled: true
