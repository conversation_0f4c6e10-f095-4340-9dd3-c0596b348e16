import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_provider.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_state.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store_provider.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store_state.dart';

void main() {
  group('Bookkeeping Riverpod Migration Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('BookkeepingState should have correct initial values', () {
      final state = BookkeepingState();
      
      expect(state.selectedTab, equals(0));
      expect(state.noNeed, equals(false));
      expect(state.isSavingInput, equals(false));
      expect(state.remarkStr, equals(''));
      expect(state.textInput, equals(''));
      expect(state.isLoading, equals(false));
      expect(state.horizontalItems.length, equals(4));
    });

    test('BookkeepingStoreState should have correct initial values', () {
      const state = BookkeepingStoreState();
      
      expect(state.bookkeepingList, isEmpty);
      expect(state.accountList, isEmpty);
      expect(state.outcomeCategories, isEmpty);
      expect(state.incomeCategories, isEmpty);
      expect(state.accountListPageId, equals(0));
      expect(state.isLoading, equals(false));
    });

    test('BookkeepingProvider should be accessible', () {
      final notifier = container.read(bookkeepingProvider.notifier);
      final state = container.read(bookkeepingProvider);
      
      expect(notifier, isNotNull);
      expect(state, isNotNull);
      expect(state.selectedTab, equals(0));
    });

    test('BookkeepingStoreProvider should be accessible', () {
      final notifier = container.read(bookkeepingStoreProvider.notifier);
      final state = container.read(bookkeepingStoreProvider);
      
      expect(notifier, isNotNull);
      expect(state, isNotNull);
      expect(state.isLoading, equals(false));
    });

    test('Convenience providers should work correctly', () {
      final bookkeepingList = container.read(bookkeepingListProvider);
      final accountList = container.read(accountListProvider);
      final outcomeCategories = container.read(outcomeCategoriesProvider);
      final incomeCategories = container.read(incomeCategoriesProvider);
      
      expect(bookkeepingList, isEmpty);
      expect(accountList, isEmpty);
      expect(outcomeCategories, isEmpty);
      expect(incomeCategories, isEmpty);
    });

    test('BookkeepingState copyWith should work correctly', () {
      final initialState = BookkeepingState();
      final updatedState = initialState.copyWith(
        selectedTab: 1,
        noNeed: true,
        remarkStr: 'Test remark',
      );
      
      expect(updatedState.selectedTab, equals(1));
      expect(updatedState.noNeed, equals(true));
      expect(updatedState.remarkStr, equals('Test remark'));
      // Other fields should remain unchanged
      expect(updatedState.isSavingInput, equals(false));
      expect(updatedState.textInput, equals(''));
    });

    test('BookkeepingStoreState copyWith should work correctly', () {
      const initialState = BookkeepingStoreState();
      final updatedState = initialState.copyWith(
        accountListPageId: 1,
        isLoading: true,
      );
      
      expect(updatedState.accountListPageId, equals(1));
      expect(updatedState.isLoading, equals(true));
      // Other fields should remain unchanged
      expect(updatedState.bookkeepingList, isEmpty);
      expect(updatedState.accountList, isEmpty);
    });

    test('ShortcutItem equality should work correctly', () {
      final item1 = ShortcutItem(type: 'date');
      final item2 = ShortcutItem(type: 'date');
      final item3 = ShortcutItem(type: 'accounts');
      
      expect(item1, equals(item2));
      expect(item1, isNot(equals(item3)));
      expect(item1.hashCode, equals(item2.hashCode));
    });
  });
}
